"use client"

import React, { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import * as z from 'zod';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogBody,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Trash2 } from 'lucide-react';

import { FormWithDetails, Question, QuestionType, QUESTION_TYPES } from '@/lib/types/form';

const questionSchema = z.object({
  type: z.nativeEnum(QuestionType),
  label: z.string().min(1, 'Question label is required').max(200, 'Label must be less than 200 characters'),
  help_text: z.string().max(500, 'Help text must be less than 500 characters').optional(),
  required: z.boolean(),
  options: z.array(z.object({
    label: z.string().min(1, 'Option label is required'),
    value: z.string().min(1, 'Option value is required'),
  })).optional(),
});

type QuestionData = z.infer<typeof questionSchema>;

interface EditQuestionDialogProps {
  question: Question;
  form: FormWithDetails;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: QuestionData) => Promise<void>;
}

export function EditQuestionDialog({
  question,
  form: formData,
  open,
  onOpenChange,
  onSave
}: EditQuestionDialogProps) {
  const form = useForm<QuestionData>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      type: question.type,
      label: question.label,
      help_text: question.help_text || '',
      required: question.required,
      options: question.options || [],
    },
  });

  const { fields, append, remove, replace } = useFieldArray({
    control: form.control,
    name: 'options',
  });

  useEffect(() => {
    if (open) {
      form.reset({
        type: question.type,
        label: question.label,
        help_text: question.help_text || '',
        required: question.required,
        options: question.options || [],
      });
      
      // Replace options array to sync with form
      replace(question.options || []);
    }
  }, [open, question, form, replace]);

  const watchedType = form.watch('type');
  const typeInfo = QUESTION_TYPES.find(t => t.type === watchedType);
  const hasOptions = typeInfo?.hasOptions || false;

  const handleSubmit = async (data: QuestionData) => {
    try {
      // Remove options if question type doesn't support them
      if (!hasOptions) {
        data.options = undefined;
      }
      
      await onSave(data);
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating question:', error);
    }
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  const addOption = () => {
    const currentOptions = form.getValues('options') || [];
    const optionNumber = currentOptions.length + 1;
    const label = `Option ${optionNumber}`;
    append({ 
      label: label, 
      value: label.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_') 
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent size="large">
        <DialogHeader>
          <DialogTitle>Edit Question</DialogTitle>
          <DialogDescription>
            Update the question settings and options.
          </DialogDescription>
        </DialogHeader>

        <DialogBody>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* Question Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm md:text-base font-medium mb-1">Question Type</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-12 rounded-xl border-gray-200 px-4">
                          <SelectValue placeholder="Select question type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {QUESTION_TYPES.map((type) => (
                          <SelectItem key={type.type} value={type.type}>
                            <div className="flex flex-col items-start text-left">
                              <span className="font-medium">{type.label}</span>
                              <span className="text-xs text-muted-foreground">{type.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the type of input for this question.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Question Label */}
              <FormField
                control={form.control}
                name="label"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm md:text-base font-medium mb-1">Question Label</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter question label..." {...field} className="h-12 rounded-xl border-gray-200 px-4" />
                    </FormControl>
                    <FormDescription>
                      The main question text that users will see.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Help Text */}
              <FormField
                control={form.control}
                name="help_text"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm md:text-base font-medium mb-1">Help Text (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter help text..."
                        className="min-h-[60px] rounded-xl border-gray-200 px-4 py-3"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Additional instructions or context for this question.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Required */}
              <FormField
                control={form.control}
                name="required"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-xl border border-gray-200 p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base font-medium">Required Question</FormLabel>
                      <FormDescription>
                        Users must answer this question to submit the form.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Options (for select types) */}
              {hasOptions && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-sm md:text-base font-medium">Options</FormLabel>
                    <Button type="button" variant="outline" size="sm" onClick={addOption} className="h-10 px-4 rounded-xl">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Option
                    </Button>
                  </div>
                  
                  {fields.length === 0 && (
                    <Card className="border-dashed border-gray-200">
                      <CardContent className="flex items-center justify-center py-8">
                        <div className="text-center">
                          <p className="text-muted-foreground mb-2">No options added yet</p>
                          <Button type="button" variant="outline" size="sm" onClick={addOption} className="h-10 px-4 rounded-xl">
                            <Plus className="h-4 w-4 mr-2" />
                            Add First Option
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {fields.map((field, index) => (
                    <Card key={field.id} className="border border-gray-200">
                      <CardContent className="pt-4">
                        <div className="flex gap-3 items-end">
                          <div className="flex-1">
                            <FormField
                              control={form.control}
                              name={`options.${index}.label`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-sm font-medium mb-1">Label</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Option label..." 
                                      {...field}
                                      className="h-12 rounded-xl border-gray-200 px-4"
                                      onChange={(e) => {
                                        const label = e.target.value;
                                        const autoValue = label.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_');
                                        field.onChange(label);
                                        form.setValue(`options.${index}.value`, autoValue);
                                      }}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => remove(index)}
                            className="text-destructive hover:text-destructive h-10 px-4 rounded-xl"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </form>
          </Form>
        </DialogBody>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleCancel} className="h-12 px-6 rounded-xl font-semibold">
            Cancel
          </Button>
          <Button type="submit" disabled={!form.formState.isDirty} className="h-12 px-6 rounded-xl font-semibold">
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
