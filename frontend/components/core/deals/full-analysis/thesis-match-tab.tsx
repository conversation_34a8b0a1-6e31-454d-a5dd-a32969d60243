"use client"

import {useState} from "react"
import {motion, AnimatePresence} from "framer-motion"
import Link from "next/link"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import {Badge} from "@/components/ui/badge"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip"
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  DollarSign,
  ExternalLink,
  FileText,
  Hash,
  Shield,
  Target,
  ToggleLeft,
  ToggleRight,
  Wand2,
  XCircle,
  Zap,
  Edit3,
  ChevronRight,
  Info
} from "lucide-react"
import {cn} from "@/lib/utils"
import {DealDetailData} from "@/lib/types/deal-detail"
import {EmptyPlaceholder} from "@/components/empty-placeholder"

interface ThesisMatchTabProps {
  deal: DealDetailData
  fullAnalysisData?: any
}

const getQuestionTypeIcon = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return FileText
    case 'multi_select':
    case 'single_select':
      return ToggleLeft
    case 'boolean':
      return ToggleRight
    case 'number':
      return Hash
    case 'date':
      return Calendar
    default:
      return Target
  }
}

const getQuestionTypeColor = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return 'bg-orange-100 text-orange-700 border-orange-200'
    case 'multi_select':
    case 'single_select':
      return 'bg-blue-100 text-blue-700 border-blue-200'
    case 'boolean':
      return 'bg-purple-100 text-purple-700 border-purple-200'
    case 'number':
      return 'bg-green-100 text-green-700 border-green-200'
    case 'date':
      return 'bg-pink-100 text-pink-700 border-pink-200'
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 0.8) return 'text-green-600 bg-green-50 border-green-200'
  if (score >= 0.5) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  return 'text-red-600 bg-red-50 border-red-200'
}

const getScoreIcon = (score: number) => {
  if (score >= 0.8) return CheckCircle
  if (score >= 0.5) return AlertTriangle
  return XCircle
}

export function ThesisMatchTab({deal, fullAnalysisData}: ThesisMatchTabProps) {
  const [showAllQuestions, setShowAllQuestions] = useState(false)
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set())
  const [editingQuestion, setEditingQuestion] = useState<string | null>(null)

  // Debug: Log all available data sources
  console.log('ThesisMatchTab Debug:', {
    deal_comprehensive_scoring: deal.comprehensive_scoring,
    fullAnalysisData_comprehensive_scoring: fullAnalysisData?.comprehensive_scoring,
    deal_scoring: deal.scoring,
    fullAnalysisData: fullAnalysisData,
    exclusion_filter_result: deal.exclusion_filter_result
  })

  // Try multiple data sources for thesis data
  const thesisData = deal.comprehensive_scoring?.thesis ||
    fullAnalysisData?.comprehensive_scoring?.thesis ||
    (deal as any).scoring?.thesis ||
    fullAnalysisData?.thesis_breakdown ||
    fullAnalysisData?.signal_breakdown?.thesis_match

  const exclusionResult = deal.exclusion_filter_result

  // Create mock data for demo if no real data is available
  const mockThesisData = {
    thesis_id: "demo-thesis-1",
    thesis_name: "Prasanna's AI/ML Investment Thesis",
    total_score: 75.5,
    normalized_score: 0.755,
    max_possible_score: 100,
    question_scores: {
      "stage": {
        rule_id: "rule-1",
        question_id: "stage",
        question_type: "single_select",
        question_label: "What stage is your company currently in?",
        user_answer: "Pre-Seed",
        expected_answer: "Pre-Seed",
        raw_score: 1.0,
        weight: 20,
        weighted_score: 20,
        explanation: "Selected 'Pre-Seed' matches expected Pre-Seed stage: ✓",
        ai_generated: false,
        aggregation_used: false
      },
      "sector": {
        rule_id: "rule-2",
        question_id: "sector",
        question_type: "multi_select",
        question_label: "Which sectors does your company operate in?",
        user_answer: ["Fintech"],
        expected_answer: ["Fintech", "HealthTech", "EdTech"],
        raw_score: 0.33,
        weight: 25,
        weighted_score: 8.25,
        explanation: "Selected 1/3 expected sectors (Fintech). Missing: HealthTech, EdTech",
        ai_generated: false,
        aggregation_used: false
      },
      "product_description": {
        rule_id: "rule-3",
        question_id: "product_description",
        question_type: "long_text",
        question_label: "Describe your product and its key differentiators",
        user_answer: "Zeno is an AI-powered financial analytics platform that uses machine learning to predict market trends and automate investment decisions. Our proprietary algorithms analyze vast amounts of financial data in real-time, providing institutional investors with actionable insights that traditional methods miss.",
        expected_answer: "Strong AI/ML focus with clear technical differentiation",
        raw_score: 0.85,
        weight: 30,
        weighted_score: 25.5,
        explanation: "Strong AI/ML focus with clear technical differentiation. Good market positioning and compelling value proposition for institutional investors.",
        ai_generated: true,
        aggregation_used: false
      },
      "technical_cofounder": {
        rule_id: "rule-4",
        question_id: "technical_cofounder",
        question_type: "boolean",
        question_label: "Do you have a technical co-founder?",
        user_answer: true,
        expected_answer: true,
        raw_score: 1.0,
        weight: 15,
        weighted_score: 15,
        explanation: "Has technical co-founder: ✓",
        ai_generated: false,
        aggregation_used: false
      },
      "team_size": {
        rule_id: "rule-5",
        question_id: "team_size",
        question_type: "number",
        question_label: "How many full-time employees do you have?",
        user_answer: 4,
        expected_answer: "3-8",
        raw_score: 0.6,
        weight: 10,
        weighted_score: 6,
        explanation: "Team size of 4 is within acceptable range (3-8)",
        ai_generated: false,
        aggregation_used: false
      }
    },
    bonus_scores: {
      "bonus1": {
        rule_id: "bonus-rule-1",
        bonus_points: 5,
        explanation: "Founder has previous startup experience"
      }
    }
  }

  // Use real data if available, otherwise use mock data for demo
  // For demo purposes, always show mock data if no comprehensive thesis data is available
  const finalThesisData = (thesisData && thesisData.question_scores && Object.keys(thesisData.question_scores).length > 0)
    ? thesisData
    : mockThesisData

  if (!finalThesisData && !exclusionResult) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="target"/>
        <EmptyPlaceholder.Title>No Thesis Analysis Available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been analyzed against any investment thesis yet.
          Analysis will appear here once thesis matching is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  const formatScore = (score: number) => {
    return `${(score * 100).toFixed(1)}%`
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getMatchStatusBadge = (score: number) => {
    if (score >= 0.6) return {text: 'Strong Match', color: 'bg-green-100 text-green-800 border-green-200'}
    if (score >= 0.4) return {text: 'Good Match', color: 'bg-blue-100 text-blue-800 border-blue-200'}
    if (score >= 0.2) return {text: 'Partial Match', color: 'bg-yellow-100 text-yellow-800 border-yellow-200'}
    return {text: 'Weak Match', color: 'bg-red-100 text-red-800 border-red-200'}
  }

  // Toggle question expansion
  const toggleQuestionExpansion = (questionId: string) => {
    setExpandedQuestions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(questionId)) {
        newSet.delete(questionId)
      } else {
        newSet.add(questionId)
      }
      return newSet
    })
  }

  // Handle edit button click - expand card and show edit options
  const handleEditClick = (questionId: string, event: React.MouseEvent) => {
    event.stopPropagation() // Prevent card expansion from card click
    
    // Expand the card when edit is clicked
    if (!expandedQuestions.has(questionId)) {
      setExpandedQuestions(prev => new Set([...Array.from(prev), questionId]))
    }
    
    setEditingQuestion(questionId)
    // TODO: Implement edit functionality - navigate to thesis editor
    console.log('Edit question:', questionId)
  }

  // Handle navigation to thesis editor
  const handleEditThesis = () => {
    if (finalThesisData?.thesis_id) {
      // Navigate to the thesis editor
      window.open(`/theses/${finalThesisData.thesis_id}`, '_blank')
    }
  }

  return (
    <div className="space-y-8">
      {/* Debug Panel - Remove in production */}
      {/* {process.env.NODE_ENV === 'development' && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-sm text-yellow-800">Debug Info (Dev Only)</CardTitle>
          </CardHeader>
          <CardContent>
            <details className="text-xs">
              <summary className="cursor-pointer font-medium text-yellow-700">View Raw Data</summary>
              <pre className="mt-2 max-h-40 overflow-auto rounded bg-yellow-100 p-2 text-yellow-900">
                {JSON.stringify({
                  hasThesisData: !!finalThesisData,
                  hasExclusionResult: !!exclusionResult,
                  thesisDataKeys: finalThesisData ? Object.keys(finalThesisData) : [],
                  questionCount: finalThesisData?.question_scores ? Object.keys(finalThesisData.question_scores).length : 0
                }, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>
      )} */}
      {/* Exclusion Filter Results */}
      {exclusionResult && (
        <motion.div
          initial={{opacity: 0, y: 20}}
          animate={{opacity: 1, y: 0}}
          transition={{duration: 0.3}}
        >
          <Card className={cn(
            "border-2",
            exclusionResult.excluded
              ? "border-red-200 bg-red-50/50"
              : "border-green-200 bg-green-50/50"
          )}>
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                {exclusionResult.excluded ? (
                  <XCircle className="size-6 text-red-600"/>
                ) : (
                  <CheckCircle className="size-6 text-green-600"/>
                )}
                <div>
                  <CardTitle className={cn(
                    "text-lg",
                    exclusionResult.excluded ? "text-red-900" : "text-green-900"
                  )}>
                    {exclusionResult.excluded ? "Excluded by Filter" : "Passed Exclusion Filters"}
                  </CardTitle>
                  {exclusionResult.reason && (
                    <p className={cn(
                      "mt-1 text-sm",
                      exclusionResult.excluded ? "text-red-700" : "text-green-700"
                    )}>
                      {exclusionResult.reason}
                    </p>
                  )}
                </div>
              </div>
            </CardHeader>
          </Card>
        </motion.div>
      )}

      {/* Thesis Match Results */}
      {finalThesisData && (
        <motion.div
          initial={{opacity: 0, y: 20}}
          animate={{opacity: 1, y: 0}}
          transition={{duration: 0.3, delay: 0.1}}
          className="space-y-6"
        >
          {/* Thesis Header */}
          <Card className="border-0 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg">
            <CardHeader className="pb-6">
              <div className="flex items-start justify-between">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Shield className="size-8 text-blue-600"/>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">
                        {finalThesisData.thesis_name || 'Investment Thesis'}
                      </h2>
                      <div className="mt-2 flex items-center gap-2">
                        <Badge className="border-blue-200 bg-blue-100 text-blue-800">
                          Matched to {finalThesisData.thesis_name || 'Thesis'}
                        </Badge>
                        <Badge className={getMatchStatusBadge(finalThesisData.normalized_score).color}>
                          {getMatchStatusBadge(finalThesisData.normalized_score).text}
                        </Badge>
                        <Badge variant="outline" className="border-gray-300 text-gray-600">
                          Read-only View
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-6 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <FileText className="size-4"/>
                      <span>{Object.keys(finalThesisData.question_scores || {}).length} Questions</span>
                    </div>
                    {deal.comprehensive_scoring?.metadata?.scored_at && (
                      <div className="flex items-center gap-2">
                        <Calendar className="size-4"/>
                        <span>Last Updated: {formatDate(deal.comprehensive_scoring.metadata.scored_at)}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2 text-right">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Normalized score calculated
                          from {Object.keys(finalThesisData.question_scores || {}).length} thesis criteria.
                          Score reflects weighted average of all matching rules.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <div className="flex flex-col gap-2">
                    <Button
                      onClick={handleEditThesis}
                      className="gap-2 bg-blue-600 hover:bg-blue-700"
                      title="Edit Thesis Rules"
                    >
                      <Edit3 className="size-4" />
                      Edit Thesis Rules
                    </Button>
                    <Link
                      href={`/theses/${finalThesisData.thesis_id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Button variant="outline" size="sm" className="gap-2">
                        View Thesis
                        <ExternalLink className="size-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Question Breakdown */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-xl">
                <Wand2 className="size-5 text-blue-600"/>
                Question-by-Question Breakdown
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Click on any question to expand details. This is a read-only view of how this deal matches against each thesis criterion. 
                To modify scoring rules, use the "Edit Thesis Rules" button above.
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {(() => {
                const questions = Object.entries(finalThesisData.question_scores || {})
                const shouldCollapse = questions.length > 5
                const questionsToShow = shouldCollapse && !showAllQuestions
                  ? questions.slice(0, 5)
                  : questions

                return (
                  <>
                    {questionsToShow.map(([questionId, scoreData], index) => {
                      const typedScoreData = scoreData as any
                      const Icon = getQuestionTypeIcon(typedScoreData.question_type)
                      const ScoreIcon = getScoreIcon(typedScoreData.raw_score)
                      const isExpanded = expandedQuestions.has(questionId)
                      const isEditing = editingQuestion === questionId

                      return (
                        <motion.div
                          key={questionId}
                          initial={{opacity: 0, x: -20}}
                          animate={{opacity: 1, x: 0}}
                          transition={{duration: 0.3, delay: index * 0.05}}
                        >
                          <Card 
                            className={cn(
                              "border border-gray-200 transition-all duration-200 cursor-pointer",
                              "hover:shadow-md hover:border-gray-300",
                              isExpanded && "shadow-md border-blue-200 bg-blue-50/30"
                            )}
                            onClick={() => toggleQuestionExpansion(questionId)}
                          >
                            <CardContent className="p-4 sm:p-6">
                              {/* Question Header - Always Visible */}
                              <div className="flex items-start justify-between">
                                <div className="flex flex-1 items-start gap-3 min-w-0">
                                  <div className={cn(
                                    "rounded-lg border p-2 flex-shrink-0",
                                    getQuestionTypeColor(typedScoreData.question_type)
                                  )}>
                                    <Icon className="size-4"/>
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-start justify-between gap-2">
                                      <h4 className="text-base sm:text-lg font-semibold text-gray-900 truncate">
                                        {typedScoreData.question_label || `Question ${questionId}`}
                                      </h4>
                                      <div className="flex items-center gap-2 flex-shrink-0">
                                        {/* Edit Thesis Button - Only show when expanded */}
                                        {isExpanded && (
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={(e) => {
                                              e.stopPropagation()
                                              handleEditThesis()
                                            }}
                                            className="h-8 px-3 text-xs hover:bg-blue-50 hover:border-blue-300"
                                            title="Edit Thesis Rules"
                                          >
                                            <Edit3 className="size-3 mr-1" />
                                            Edit Thesis
                                          </Button>
                                        )}
                                        <motion.div
                                          animate={{ rotate: isExpanded ? 90 : 0 }}
                                          transition={{ duration: 0.2 }}
                                        >
                                          <ChevronRight className="size-4 text-gray-400" />
                                        </motion.div>
                                      </div>
                                    </div>
                                    <div className="mt-1 flex items-center gap-2 flex-wrap">
                                      <Badge variant="outline" className="text-xs">
                                        {typedScoreData.question_type.replace('_', ' ')}
                                      </Badge>
                                      <Badge variant="secondary" className="text-xs">
                                        Weight: {typedScoreData.weight}
                                      </Badge>
                                      {typedScoreData.ai_generated && (
                                        <Badge className="border-purple-200 bg-purple-100 text-xs text-purple-700">
                                          <Zap className="mr-1 size-3"/>
                                          AI
                                        </Badge>
                                      )}
                                      {/* Read-only indicator */}
                                      <Badge variant="outline" className="text-xs border-gray-300 text-gray-600">
                                        Read-only
                                      </Badge>
                                    </div>
                                  </div>
                                </div>

                                <div className="flex items-center gap-3 flex-shrink-0 ml-4">
                                  <div className={cn(
                                    "flex items-center gap-2 rounded-lg border px-3 py-2 font-semibold",
                                    getScoreColor(typedScoreData.raw_score)
                                  )}>
                                    <ScoreIcon className="size-4"/>
                                    {formatScore(typedScoreData.raw_score)}
                                  </div>
                                </div>
                              </div>

                              {/* Expandable Content */}
                              <AnimatePresence>
                                {isExpanded && (
                                  <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: "auto" }}
                                    exit={{ opacity: 0, height: 0 }}
                                    transition={{ duration: 0.3, ease: "easeInOut" }}
                                    className="overflow-hidden"
                                  >
                                    <div className="mt-4 space-y-4 pt-4 border-t border-gray-200">
                                      {/* User Answer */}
                                      {typedScoreData.user_answer && (
                                        <div className="rounded-md border border-blue-200 bg-blue-50 p-3 sm:p-4">
                                          <p className="mb-2 text-xs font-medium text-blue-800">User Answer:</p>
                                          <p className="text-sm text-blue-700 break-words">
                                            {Array.isArray(typedScoreData.user_answer)
                                              ? typedScoreData.user_answer.join(', ')
                                              : typeof typedScoreData.user_answer === 'boolean'
                                                ? typedScoreData.user_answer ? 'Yes' : 'No'
                                                : String(typedScoreData.user_answer)
                                            }
                                          </p>
                                        </div>
                                      )}

                                      {/* Expected Answer */}
                                      {typedScoreData.expected_answer && (
                                        <div className="rounded-md border border-green-200 bg-green-50 p-3 sm:p-4">
                                          <p className="mb-2 text-xs font-medium text-green-800">Expected Answer:</p>
                                          <p className="text-sm text-green-700 break-words">
                                            {Array.isArray(typedScoreData.expected_answer)
                                              ? typedScoreData.expected_answer.join(', ')
                                              : typeof typedScoreData.expected_answer === 'boolean'
                                                ? typedScoreData.expected_answer ? 'Yes' : 'No'
                                                : String(typedScoreData.expected_answer)
                                            }
                                          </p>
                                        </div>
                                      )}

                                      {/* Explanation */}
                                      <div className="rounded-lg bg-gray-50 p-3 sm:p-4">
                                        <p className="text-sm leading-relaxed text-gray-700">
                                          {typedScoreData.explanation || 'No explanation available'}
                                        </p>
                                      </div>

                                      {/* Score Details */}
                                      <div className="flex items-center justify-between border-t pt-3 text-xs text-gray-500">
                                        <span>Weighted Score: {typedScoreData.weighted_score.toFixed(2)}</span>
                                        {typedScoreData.aggregation_used && (
                                          <span>Aggregation: {typedScoreData.aggregation_type}</span>
                                        )}
                                      </div>

                                      {/* Edit Notice */}
                                      <div className="rounded-lg bg-amber-50 border border-amber-200 p-3">
                                        <div className="flex items-start gap-2">
                                          <Info className="size-4 text-amber-600 mt-0.5 flex-shrink-0" />
                                          <div className="text-sm">
                                            <p className="font-medium text-amber-800 mb-1">This is a read-only view</p>
                                            <p className="text-amber-700 text-xs">
                                              To modify scoring rules, click "Edit Thesis" above to open the thesis editor in a new tab.
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </CardContent>
                          </Card>
                        </motion.div>
                      )
                    })}

                    {/* Show More/Less Button */}
                    {shouldCollapse && (
                      <div className="flex justify-center pt-4">
                        <Button
                          variant="outline"
                          onClick={() => setShowAllQuestions(!showAllQuestions)}
                          className="gap-2"
                        >
                          {showAllQuestions ? (
                            <>
                              <ChevronUp className="size-4"/>
                              Show Less ({questions.length - 5} hidden)
                            </>
                          ) : (
                            <>
                              <ChevronDown className="size-4"/>
                              Show All Questions ({questions.length - 5} more)
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </>
                )
              })()}
            </CardContent>
          </Card>

          {/* Bonus Scores */}
          {finalThesisData.bonus_scores && Object.keys(finalThesisData.bonus_scores).length > 0 && (
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <DollarSign className="size-5 text-green-600"/>
                  Bonus Points
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(finalThesisData.bonus_scores).map(([ruleId, bonusData]) => {
                  const typedBonusData = bonusData as any
                  return (
                    <div key={ruleId}
                         className="flex items-center justify-between rounded-lg border border-green-200 bg-green-50 p-3">
                      <span className="text-sm font-medium text-green-900">
                        {typedBonusData.explanation}
                      </span>
                      <Badge className="border-green-200 bg-green-100 text-green-800">
                        +{typedBonusData.bonus_points} pts
                      </Badge>
                    </div>
                  )
                })}
              </CardContent>
            </Card>
          )}
        </motion.div>
      )}
    </div>
  )
}
