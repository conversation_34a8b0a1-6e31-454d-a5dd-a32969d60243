"""
Comprehensive Thesis Scoring Engine

This module implements the complete thesis scoring logic as specified in the PRD.
Handles all question types, aggregation methods, bonus scoring, and AI evaluation.
"""

import asyncio
import re
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from app.core.logging import get_logger
from app.models.form import QuestionType
from app.models.thesis import (
    AggregationType,
    CompoundCondition,
    ConditionOperator,
    FilterCondition,
    LogicalOperator,
    RuleType,
    ScoringRule,
)
from app.services.ai.text_scoring import get_text_scoring_service
from app.utils.scoring.scoring import evaluate_condition

logger = get_logger(__name__)


class ComprehensiveThesisScoringEngine:
    """
    Comprehensive scoring engine that implements all PRD requirements.

    Features:
    - All question types (text, numeric, select, boolean, date, file)
    - AI-powered text scoring with OpenAI
    - Repeatable section aggregation
    - Bonus scoring logic
    - Modular result storage
    """

    def __init__(self):
        """Initialize the scoring engine."""
        self.text_scoring_service = None

    async def _get_text_scoring_service(self):
        """Lazy load text scoring service."""
        if self.text_scoring_service is None:
            self.text_scoring_service = await get_text_scoring_service()
        return self.text_scoring_service

    async def calculate_comprehensive_score(
        self,
        thesis_with_rules,
        form_responses: Dict[str, Any],
        form_details=None,
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive thesis score following PRD specifications.

        Args:
            thesis_with_rules: Thesis object with expanded rules
            form_responses: User's form responses
            form_details: Form structure with questions and sections

        Returns:
            Comprehensive scoring result with modular structure
        """
        try:
            # Initialize scoring structure
            scoring_result = {
                "thesis": {
                    "thesis_id": str(thesis_with_rules.id),
                    "thesis_name": thesis_with_rules.name,
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "max_possible_score": 0.0,
                    "question_scores": {},
                    "bonus_scores": {},
                    "scoring_details": [],
                },
                "founders": {
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "ai_analysis": "",
                    "key_insights": [],
                },
                "market": {
                    "total_score": 0.0,
                    "normalized_score": 0.0,
                    "ai_analysis": "",
                    "key_insights": [],
                },
                "metadata": {
                    "scoring_version": "v2.0",
                    "scored_at": int(datetime.now(timezone.utc).timestamp()),
                    "total_rules_processed": 0,
                    "ai_scoring_used": False,
                },
            }

            # Process scoring rules
            total_score = 0.0
            max_possible_score = 0.0
            ai_scoring_used = False

            for rule in thesis_with_rules.scoring_rules:
                if rule.is_deleted or rule.rule_type != RuleType.SCORING:
                    continue

                try:
                    # Calculate rule score
                    question_meta = None
                    if form_details:
                        for section in form_details.sections:  # type: ignore
                            for question in section.questions:  # type: ignore
                                if str(question.id) == str(rule.question_id):
                                    question_meta = question
                                    break

                    rule_result = await self._calculate_rule_score(
                        rule, form_responses, form_details
                    )

                    if rule_result["ai_used"]:
                        ai_scoring_used = True

                    # Apply weight
                    weighted_score = rule_result["score"] * rule.weight
                    total_score += weighted_score
                    max_possible_score += rule.weight

                    # Store question score
                    question_id = str(rule.question_id)

                    scoring_result["thesis"]["question_scores"][question_id] = {
                        "rule_id": str(rule.id),
                        "question_id": question_id,
                        "question_type": question_meta.type.value
                        if question_meta
                        else "unknown",
                        "question_label": question_meta.label
                        if question_meta
                        else "unknown",
                        "raw_score": rule_result["score"],
                        "weight": rule.weight,
                        "weighted_score": weighted_score,
                        "explanation": rule_result.get("explanation", ""),
                        "sources": rule_result.get("sources", []),
                        "ai_generated": rule_result.get("ai_generated", False),
                        "aggregation_used": rule_result.get("aggregation_used", False),
                        "aggregation_type": rule_result.get("aggregation_type", None),
                    }

                    # Add to scoring details
                    scoring_result["thesis"]["scoring_details"].append({
                        "rule_id": str(rule.id),
                        "question_id": question_id,
                        "score": rule_result["score"],
                        "weight": rule.weight,
                        "weighted_score": weighted_score,
                        "explanation": rule_result.get("explanation", ""),
                    })

                    scoring_result["metadata"]["total_rules_processed"] += 1

                except Exception as e:
                    logger.error(
                        f"Error processing scoring rule {rule.id}: {str(e)}",
                        exc_info=True,
                    )
                    continue

            # Process bonus rules
            for rule in thesis_with_rules.scoring_rules:
                if rule.is_deleted or rule.rule_type != RuleType.BONUS:
                    continue

                try:
                    bonus_result = await self._calculate_bonus_rule_score(
                        rule, form_responses, form_details
                    )

                    if bonus_result["bonus_awarded"] > 0:
                        bonus_id = f"bonus_{rule.id}"
                        scoring_result["thesis"]["bonus_scores"][bonus_id] = {
                            "rule_id": str(rule.id),
                            "bonus_points": bonus_result["bonus_awarded"],
                            "reason": bonus_result["reason"],
                            "matched_answers": bonus_result["matched_answers"],
                        }
                        total_score += bonus_result["bonus_awarded"]

                except Exception as e:
                    logger.error(
                        f"Error processing bonus rule {rule.id}: {str(e)}",
                        exc_info=True,
                    )
                    continue

            # Calculate normalized score
            normalized_score = 0.0
            if max_possible_score > 0:
                normalized_score = min(100.0, (total_score / max_possible_score) * 100)

            # Update thesis scoring
            scoring_result["thesis"]["total_score"] = total_score
            scoring_result["thesis"]["normalized_score"] = normalized_score
            scoring_result["thesis"]["max_possible_score"] = max_possible_score
            scoring_result["metadata"]["ai_scoring_used"] = ai_scoring_used

            # Generate AI analysis for founders and market (placeholder for now)
            scoring_result["founders"] = await self._generate_founder_analysis(
                form_responses
            )
            scoring_result["market"] = await self._generate_market_analysis(
                form_responses
            )

            return scoring_result

        except Exception as e:
            logger.error(f"Error in comprehensive scoring: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "thesis": {"normalized_score": 0.0},
                "founders": {"normalized_score": 0.0},
                "market": {"normalized_score": 0.0},
            }

    async def _calculate_rule_score(
        self,
        rule: ScoringRule,
        form_responses: Dict[str, Any],
        form_details=None,
    ) -> Dict[str, Any]:
        """Calculate score for a single scoring rule."""
        try:
            # Handle regular scoring rule
            if not rule.question_id:
                return {
                    "score": 0.0,
                    "explanation": "No question ID specified",
                    "ai_used": False,
                }

            # Check if this is a repeatable section question
            qid = str(rule.question_id)
            instance_keys = [k for k in form_responses if k.startswith(f"{qid}_")]

            if instance_keys:
                # This is a repeatable section - evaluate each instance
                total_score = 0.0
                matched_instances = 0
                total_instances = len(instance_keys)

                for k in sorted(instance_keys, key=lambda x: int(x.split("_")[-1])):
                    idx = int(k.split("_")[-1])
                    match = await self._evaluate_condition(
                        rule.condition, form_responses, idx
                    )
                    if match:
                        matched_instances += 1
                        total_score += rule.weight

                explanation = (
                    f"{matched_instances} of {total_instances} instances matched"
                )
                return {
                    "score": total_score,
                    "explanation": explanation,
                    "ai_used": False,
                    "aggregation_used": True,
                    "aggregation_type": "per_instance",
                }
            else:
                # Single question - get response and evaluate
                response = self._get_question_response(qid, form_responses)

                # Get question metadata
                question_meta = None
                if form_details:
                    for section in form_details.sections:  # type: ignore
                        for question in section.questions:  # type: ignore
                            if str(question.id) == qid:
                                question_meta = question
                                break

                if response is None:
                    return {
                        "score": 0.0,
                        "explanation": "Question not answered",
                        "ai_used": False,
                    }

                # Score based on question type
                if question_meta and question_meta.type in [
                    QuestionType.SHORT_TEXT,
                    QuestionType.LONG_TEXT,
                ]:
                    return await self._score_text_question(rule, response, form_details)
                elif question_meta and question_meta.type == QuestionType.NUMBER:
                    return await self._score_numeric_question(rule, response)
                elif question_meta and question_meta.type == QuestionType.RANGE:
                    return await self._score_range_question(rule, response)
                elif question_meta and question_meta.type == QuestionType.SINGLE_SELECT:
                    return await self._score_single_select_question(rule, response)
                elif question_meta and question_meta.type == QuestionType.MULTI_SELECT:
                    return await self._score_multi_select_question(rule, response)
                elif question_meta and question_meta.type == QuestionType.BOOLEAN:
                    return await self._score_boolean_question(rule, response)
                elif question_meta and question_meta.type == QuestionType.DATE:
                    return await self._score_date_question(rule, response)
                elif question_meta and question_meta.type == QuestionType.FILE:
                    return {
                        "score": 0.0,
                        "explanation": "File questions are not scored",
                        "ai_used": False,
                    }
                else:
                    # Fallback: evaluate condition directly
                    match = await self._evaluate_condition(
                        rule.condition, form_responses
                    )
                    score = rule.weight if match else 0.0
                    explanation = "Matched" if match else "Did not match"
                    return {
                        "score": score,
                        "explanation": explanation,
                        "ai_used": False,
                    }

        except Exception as e:
            logger.error(f"Error calculating rule score: {str(e)}", exc_info=True)
            return {"score": 0.0, "explanation": f"Error: {str(e)}", "ai_used": False}

    async def _calculate_bonus_rule_score(
        self,
        rule: ScoringRule,
        form_responses: Dict[str, Any],
        form_details=None,
    ) -> Dict[str, Any]:
        """Calculate bonus score for a bonus rule with aggregation support."""
        try:
            bonus_points = rule.bonus_points or 0.0
            matched_answers = []

            # Handle compound conditions (AND, OR, NOT)
            if isinstance(rule.condition, CompoundCondition):
                # Evaluate the compound condition
                match = await self._evaluate_condition(rule.condition, form_responses)
                matched_answers = [match]
                award = bonus_points if match else 0.0
                reason = (
                    "Compound condition matched"
                    if match
                    else "Compound condition did not match"
                )

                return {
                    "bonus_awarded": award,
                    "matched_answers": matched_answers,
                    "reason": reason,
                }

            # Handle simple conditions with section_id and aggregation
            elif isinstance(rule.condition, FilterCondition):
                qid = str(rule.question_id)
                section_id = getattr(rule.condition, "section_id", None)
                aggregation = getattr(rule.condition, "aggregation", None)
                threshold = getattr(rule.condition, "aggregate_threshold", None)

                # If section_id is specified, look for question_id_{i} pattern
                if section_id and qid:
                    instance_keys = [
                        k for k in form_responses if k.startswith(f"{qid}_")
                    ]

                    if instance_keys:
                        # This is a repeatable section - evaluate each instance
                        values = []
                        for k in sorted(
                            instance_keys, key=lambda x: int(x.split("_")[-1])
                        ):
                            idx = int(k.split("_")[-1])
                            match = await self._evaluate_condition(
                                rule.condition, form_responses, idx
                            )
                            matched_answers.append(match)
                            v = form_responses.get(k)
                            values.append(v)

                        total = len(matched_answers)
                        count = sum(1 for m in matched_answers if m)
                        pct = (count / total * 100) if total > 0 else 0

                        # For SUM aggregation, sum numeric values of matching instances
                        sum_val = 0.0
                        for v, m in zip(values, matched_answers):
                            if m and isinstance(v, (int, float)):
                                sum_val += float(v)
                            elif (
                                m
                                and isinstance(v, str)
                                and v.replace(".", "", 1).isdigit()
                            ):
                                sum_val += float(v)

                        # Determine award based on aggregation logic
                        award = 0.0
                        reason = ""

                        if aggregation == AggregationType.ANY:
                            award = bonus_points if count > 0 else 0.0
                            reason = f"ANY: {count} of {total} instances matched"
                        elif aggregation == AggregationType.ALL:
                            award = (
                                bonus_points if count == total and total > 0 else 0.0
                            )
                            reason = f"ALL: {count} of {total} instances matched"
                        elif aggregation == AggregationType.COUNT:
                            award = bonus_points if count >= (threshold or 1) else 0.0
                            reason = f"COUNT: {count} of {total} instances matched (threshold: {threshold})"
                        elif aggregation == AggregationType.PERCENTAGE:
                            award = bonus_points if pct >= (threshold or 50) else 0.0
                            reason = f"PERCENTAGE: {pct:.1f}% matched (threshold: {threshold}%)"
                        elif aggregation == AggregationType.SUM:
                            award = bonus_points if sum_val >= (threshold or 0) else 0.0
                            reason = f"SUM: {sum_val} >= {threshold}"
                        else:
                            # Default to ANY behavior
                            award = bonus_points if count > 0 else 0.0
                            reason = (
                                f"Default (ANY): {count} of {total} instances matched"
                            )

                        return {
                            "bonus_awarded": award,
                            "matched_answers": matched_answers,
                            "reason": reason,
                        }
                    else:
                        # No instances found
                        return {
                            "bonus_awarded": 0.0,
                            "matched_answers": [],
                            "reason": f"No instances found for question {qid}",
                        }
                else:
                    # Single question bonus rule
                    match = await self._evaluate_condition(
                        rule.condition, form_responses
                    )
                    matched_answers = [match]
                    award = bonus_points if match else 0.0
                    reason = "Matched" if match else "Did not match"

                    return {
                        "bonus_awarded": award,
                        "matched_answers": matched_answers,
                        "reason": reason,
                    }
            else:
                # No specific question - evaluate condition directly
                match = await self._evaluate_condition(rule.condition, form_responses)
                matched_answers = [match]
                award = bonus_points if match else 0.0
                reason = "Matched" if match else "Did not match"

                return {
                    "bonus_awarded": award,
                    "matched_answers": matched_answers,
                    "reason": reason,
                }

        except Exception as e:
            logger.error(f"Error calculating bonus rule score: {str(e)}", exc_info=True)
            return {
                "bonus_awarded": 0.0,
                "matched_answers": [],
                "reason": f"Error: {str(e)}",
            }

    def _get_question_response(
        self, question_id: str, form_responses: Dict[str, Any]
    ) -> Any:
        """
        Get response for a question from form responses, handling both flat and structured formats.

        Form responses can be:
        1. Flat format: {question_id: answer, ...}
        2. Structured format: {answers: {question_id: answer}, repeatable_answers: {...}}
        3. Submission format: Direct answers dict
        """
        # Try direct lookup first (flat format or submission.answers)
        if question_id in form_responses:
            return form_responses[question_id]

        # Try structured format
        if "answers" in form_responses and isinstance(form_responses["answers"], dict):
            if question_id in form_responses["answers"]:
                return form_responses["answers"][question_id]

        # Try repeatable answers format
        if "repeatable_answers" in form_responses:
            repeatable_answers = form_responses["repeatable_answers"]
            if isinstance(repeatable_answers, dict):
                for section_id, instances in repeatable_answers.items():
                    if isinstance(instances, dict):
                        for instance_id, instance_answers in instances.items():
                            if (
                                isinstance(instance_answers, dict)
                                and question_id in instance_answers
                            ):
                                return instance_answers[question_id]

        return None

    async def _evaluate_condition(
        self,
        condition: Union[FilterCondition, CompoundCondition],
        form_responses: Dict[str, Any],
        instance_idx: Optional[int] = None,
    ) -> bool:
        """Evaluate a condition against form responses."""
        try:
            if isinstance(condition, FilterCondition):
                # Get the response value
                qid = str(condition.question_id)
                section_id = getattr(condition, "section_id", None)
                aggregation = getattr(condition, "aggregation", None)
                threshold = getattr(condition, "aggregate_threshold", None)

                # If section_id is specified, this is a repeatable section
                if section_id and qid:
                    # Look for question_id_{i} pattern
                    instance_keys = [
                        k for k in form_responses if k.startswith(f"{qid}_")
                    ]

                    if not instance_keys:
                        return False

                    # If instance_idx is provided, evaluate that specific instance
                    if instance_idx is not None:
                        response = form_responses.get(f"{qid}_{instance_idx}")
                        if response is None:
                            return False

                        # Evaluate based on operator
                        if condition.operator is None:
                            return False
                        return evaluate_condition(
                            {condition.operator.value: condition.value}, response
                        )

                    # Otherwise, evaluate all instances based on aggregation
                    matches = []
                    values = []

                    for k in sorted(instance_keys, key=lambda x: int(x.split("_")[-1])):
                        response = form_responses.get(k)
                        if response is not None:
                            if condition.operator is None:
                                matches.append(False)
                            else:
                                match = evaluate_condition(
                                    {condition.operator.value: condition.value},
                                    response,
                                )
                                matches.append(match)
                                values.append(response)
                        else:
                            matches.append(False)

                    if not matches:
                        return False

                    # Apply aggregation logic
                    count = sum(1 for m in matches if m)
                    total = len(matches)
                    pct = (count / total * 100) if total > 0 else 0

                    # Sum numeric values for SUM aggregation
                    sum_val = 0.0
                    for v, m in zip(values, matches):
                        if m and isinstance(v, (int, float)):
                            sum_val += float(v)
                        elif (
                            m and isinstance(v, str) and v.replace(".", "", 1).isdigit()
                        ):
                            sum_val += float(v)

                    if aggregation == AggregationType.ANY:
                        return count > 0
                    elif aggregation == AggregationType.ALL:
                        return count == total and total > 0
                    elif aggregation == AggregationType.COUNT:
                        return count >= (threshold or 1)
                    elif aggregation == AggregationType.PERCENTAGE:
                        return pct >= (threshold or 50)
                    elif aggregation == AggregationType.SUM:
                        return sum_val >= (threshold or 0)
                    else:
                        # Default to ANY behavior
                        return count > 0
                else:
                    # Single question - get response directly
                    if instance_idx is not None:
                        response = form_responses.get(f"{qid}_{instance_idx}")
                    else:
                        response = form_responses.get(qid)

                    if response is None:
                        return False

                    # Evaluate based on operator
                    if condition.operator is None:
                        return False

                    return evaluate_condition(
                        {condition.operator.value: condition.value}, response
                    )

            elif isinstance(condition, CompoundCondition):
                # Handle compound conditions using asyncio.gather to properly handle async operations
                if condition.operator == LogicalOperator.AND:
                    # Evaluate all conditions concurrently and check if all are True
                    results = await asyncio.gather(*[
                        self._evaluate_condition(c, form_responses, instance_idx)
                        for c in condition.conditions
                    ])
                    return all(results)
                elif condition.operator == LogicalOperator.OR:
                    # Evaluate all conditions concurrently and check if any is True
                    results = await asyncio.gather(*[
                        self._evaluate_condition(c, form_responses, instance_idx)
                        for c in condition.conditions
                    ])
                    return any(results)
                elif condition.operator == LogicalOperator.NOT:
                    # Evaluate the single condition and negate it
                    if condition.conditions:
                        result = await self._evaluate_condition(
                            condition.conditions[0], form_responses, instance_idx
                        )
                        return not result
                    return False

            return False
        except Exception as e:
            logger.error(f"Error evaluating condition: {str(e)}", exc_info=True)
            return False

    async def _score_text_question(
        self, rule: ScoringRule, response: Any, form_details=None
    ) -> Dict[str, Any]:
        """Score text questions using AI evaluation."""
        try:
            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                condition_value = rule.condition.value
            else:
                # For CompoundCondition or other types, we can't extract value directly
                return {
                    "score": 0.0,
                    "explanation": "Text scoring requires FilterCondition with good/bad references",
                    "ai_used": False,
                }

            # Extract good/bad references from condition.value
            if not isinstance(condition_value, dict):
                return {
                    "score": 0.0,
                    "explanation": "Invalid text scoring configuration",
                    "ai_used": False,
                }

            good_reference = condition_value.get("good_reference", "")  # type: ignore
            bad_reference = condition_value.get("bad_reference", "")  # type: ignore

            if not good_reference or not bad_reference:
                return {
                    "score": 0.0,
                    "explanation": "Missing good/bad references",
                    "ai_used": False,
                }

            # Get question label from form details
            question_label = f"Question {rule.question_id}"
            if form_details and hasattr(form_details, "sections"):
                for section in form_details.sections:
                    for question in section.questions:
                        if str(question.id) == str(rule.question_id):
                            question_label = question.label
                            break

            # Use AI scoring service
            text_scoring_service = await self._get_text_scoring_service()
            ai_result = await text_scoring_service.score_text_response(
                question_label=question_label,
                user_answer=str(response),
                good_reference=good_reference,
                bad_reference=bad_reference,
            )

            return {
                "score": ai_result["score"],
                "explanation": ai_result["explanation"],
                "sources": ai_result.get("sources", []),
                "key_strengths": ai_result.get("key_strengths", []),
                "key_weaknesses": ai_result.get("key_weaknesses", []),
                "confidence": ai_result.get("confidence", 0.8),
                "ai_generated": ai_result.get("ai_generated", True),
                "ai_used": True,
            }

        except Exception as e:
            logger.error(f"Error in text scoring: {str(e)}", exc_info=True)
            return {
                "score": 0.0,
                "explanation": f"Text scoring error: {str(e)}",
                "ai_used": False,
            }

    async def _score_numeric_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score numeric questions using operator-based comparison."""
        try:
            user_value = float(response)

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = float(rule.condition.value)
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Numeric scoring requires FilterCondition",
                    "ai_used": False,
                }

            if operator == ConditionOperator.EQUALS:
                score = 1.0 if user_value == expected_value else 0.0
            elif operator == ConditionOperator.NOT_EQUALS:
                score = 1.0 if user_value != expected_value else 0.0
            elif operator == ConditionOperator.GREATER_THAN:
                score = 1.0 if user_value > expected_value else 0.0
            elif operator == ConditionOperator.GREATER_THAN_EQUALS:
                score = 1.0 if user_value >= expected_value else 0.0
            elif operator == ConditionOperator.LESS_THAN:
                score = 1.0 if user_value < expected_value else 0.0
            elif operator == ConditionOperator.LESS_THAN_EQUALS:
                score = 1.0 if user_value <= expected_value else 0.0
            else:
                score = 0.0

            explanation = f"User value {user_value} {operator.value if operator else 'unknown'} expected {expected_value}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except (ValueError, TypeError) as e:
            return {
                "score": 0.0,
                "explanation": f"Invalid numeric value: {str(e)}",
                "ai_used": False,
            }

    async def _generate_founder_analysis(
        self, form_responses: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate AI analysis for founder signals (placeholder)."""
        # TODO: Implement comprehensive founder analysis
        return {
            "total_score": 85.0,
            "normalized_score": 85.0,
            "ai_analysis": "Founder analysis will be implemented in future iteration",
            "key_insights": [],
        }

    async def _generate_market_analysis(
        self, form_responses: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate AI analysis for market signals (placeholder)."""
        # TODO: Implement comprehensive market analysis
        return {
            "total_score": 80.0,
            "normalized_score": 80.0,
            "ai_analysis": "Market analysis will be implemented in future iteration",
            "key_insights": [],
        }

    async def _score_range_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score range questions using operator-based comparison."""
        try:
            # Range responses are typically [min, max] arrays
            if isinstance(response, list) and len(response) == 2:
                user_min, user_max = float(response[0]), float(response[1])
                user_value = (user_min + user_max) / 2  # Use midpoint for comparison
            else:
                user_value = float(response)  # type: ignore

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = rule.condition.value
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Range scoring requires FilterCondition",
                    "ai_used": False,
                }

            # Use same logic as numeric questions
            if operator == ConditionOperator.EQUALS:
                score = 1.0 if abs(user_value - float(expected_value)) < 0.01 else 0.0
            elif operator == ConditionOperator.GREATER_THAN:
                score = 1.0 if user_value > float(expected_value) else 0.0
            elif operator == ConditionOperator.GREATER_THAN_EQUALS:
                score = 1.0 if user_value >= float(expected_value) else 0.0
            elif operator == ConditionOperator.LESS_THAN:
                score = 1.0 if user_value < float(expected_value) else 0.0
            elif operator == ConditionOperator.LESS_THAN_EQUALS:
                score = 1.0 if user_value <= float(expected_value) else 0.0
            elif operator == ConditionOperator.BETWEEN:
                if isinstance(expected_value, list) and len(expected_value) == 2:
                    min_val, max_val = expected_value
                    score = 1.0 if min_val <= user_value <= max_val else 0.0
                else:
                    score = 0.0
            else:
                score = 0.0

            explanation = f"Range value {user_value} {operator.value if operator else 'unknown'} expected {expected_value}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except (ValueError, TypeError) as e:
            return {
                "score": 0.0,
                "explanation": f"Invalid range value: {str(e)}",
                "ai_used": False,
            }

    async def _score_single_select_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score single select questions using operator-based comparison."""
        try:
            user_value = str(response)

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = rule.condition.value
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Single select scoring requires FilterCondition",
                    "ai_used": False,
                }

            expected_values = (
                [str(expected_value)]
                if not isinstance(expected_value, list)
                else [str(v) for v in expected_value]
            )

            user_value_str = str(user_value)

            if operator == ConditionOperator.EQUALS:
                score = 1.0 if user_value_str in expected_values else 0.0

            elif operator == ConditionOperator.NOT_EQUALS:
                score = 1.0 if user_value_str not in expected_values else 0.0

            elif operator == ConditionOperator.IN:
                score = 1.0 if user_value_str in expected_values else 0.0

            elif operator == ConditionOperator.NOT_IN:
                score = 1.0 if user_value_str not in expected_values else 0.0

            else:
                score = 0.0

            explanation = f"Selected '{user_value}' {operator.value if operator else 'unknown'} expected {expected_value}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except Exception as e:
            return {
                "score": 0.0,
                "explanation": f"Single select scoring error: {str(e)}",
                "ai_used": False,
            }

    async def _score_multi_select_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score multi select questions with proportional scoring."""
        try:
            user_values = response if isinstance(response, list) else [response]
            user_values = [str(v) for v in user_values]

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_values = rule.condition.value
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Multi select scoring requires FilterCondition",
                    "ai_used": False,
                }

            if not isinstance(expected_values, list):
                expected_values = [expected_values]
            expected_values = [str(v) for v in expected_values]

            if operator == ConditionOperator.CONTAINS:
                # Proportional credit for overlap
                overlap = len(set(user_values).intersection(set(expected_values)))
                score = overlap / len(expected_values) if expected_values else 0.0
                explanation = (
                    f"Selected {overlap}/{len(expected_values)} expected options"
                )
            elif operator == ConditionOperator.IN:
                # Any one expected option present
                score = 1.0 if any(v in expected_values for v in user_values) else 0.0
                explanation = f"At least one expected option selected: {'✓' if score > 0 else '✗'}"
            elif operator == ConditionOperator.NOT_IN:
                # None of the expected options present
                score = (
                    1.0 if not any(v in expected_values for v in user_values) else 0.0
                )
                explanation = (
                    f"No forbidden options selected: {'✓' if score > 0 else '✗'}"
                )
            else:
                # Default to contains behavior
                overlap = len(set(user_values).intersection(set(expected_values)))
                score = overlap / len(expected_values) if expected_values else 0.0
                explanation = f"Selected {overlap}/{len(expected_values)} expected options (default)"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except Exception as e:
            return {
                "score": 0.0,
                "explanation": f"Multi select scoring error: {str(e)}",
                "ai_used": False,
            }

    async def _score_boolean_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score boolean questions using exact match."""
        try:
            # Handle string "true"/"false" values from form submissions
            if isinstance(response, str):
                user_value = response.lower() == "true"
            else:
                user_value = bool(response)

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = rule.condition.value
                operator = rule.condition.operator

                # Handle string "true"/"false" values in condition
                if isinstance(expected_value, str):
                    expected_value = expected_value.lower() == "true"
                else:
                    expected_value = bool(expected_value)
            else:
                return {
                    "score": 0.0,
                    "explanation": "Boolean scoring requires FilterCondition",
                    "ai_used": False,
                }

            if operator == ConditionOperator.EQUALS:
                score = 1.0 if user_value == expected_value else 0.0
            elif operator == ConditionOperator.NOT_EQUALS:
                score = 1.0 if user_value != expected_value else 0.0
            else:
                score = 0.0

            explanation = f"Boolean {user_value} {operator.value if operator else 'unknown'} expected {expected_value}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except Exception as e:
            return {
                "score": 0.0,
                "explanation": f"Boolean scoring error: {str(e)}",
                "ai_used": False,
            }

    async def _score_date_question(
        self, rule: ScoringRule, response: Any
    ) -> Dict[str, Any]:
        """Score date questions using date comparison."""
        try:
            from datetime import datetime

            # Parse user date
            if isinstance(response, str):
                user_date = datetime.fromisoformat(response.replace("Z", "+00:00"))
            elif isinstance(response, (int, float)):
                user_date = datetime.fromtimestamp(response)
            else:
                user_date = response

            # Handle FilterCondition vs CompoundCondition
            if isinstance(rule.condition, FilterCondition):
                expected_value = rule.condition.value
                operator = rule.condition.operator
            else:
                return {
                    "score": 0.0,
                    "explanation": "Date scoring requires FilterCondition",
                    "ai_used": False,
                }

            # Parse expected date
            if isinstance(expected_value, str):
                expected_date = datetime.fromisoformat(
                    expected_value.replace("Z", "+00:00")
                )
            elif isinstance(expected_value, (int, float)):
                expected_date = datetime.fromtimestamp(expected_value)
            else:
                expected_date = expected_value

            if operator == ConditionOperator.EQUALS:
                score = 1.0 if user_date.date() == expected_date.date() else 0.0
            elif operator == ConditionOperator.NOT_EQUALS:
                score = 1.0 if user_date.date() != expected_date.date() else 0.0
            elif operator == ConditionOperator.GREATER_THAN:
                score = 1.0 if user_date > expected_date else 0.0
            elif operator == ConditionOperator.LESS_THAN:
                score = 1.0 if user_date < expected_date else 0.0
            elif operator == ConditionOperator.GREATER_THAN_EQUALS:
                score = 1.0 if user_date >= expected_date else 0.0
            elif operator == ConditionOperator.LESS_THAN_EQUALS:
                score = 1.0 if user_date <= expected_date else 0.0
            else:
                score = 0.0

            explanation = f"Date {user_date.date()} {operator.value if operator else 'unknown'} expected {expected_date.date()}: {'✓' if score > 0 else '✗'}"

            return {
                "score": score,
                "explanation": explanation,
                "ai_used": False,
            }

        except Exception as e:
            return {
                "score": 0.0,
                "explanation": f"Date scoring error: {str(e)}",
                "ai_used": False,
            }


# --- Utility functions for the new PRD-compliant scoring ---


def extract_repeatable_answers(answers: Dict[str, Any], question_id: str) -> List[Any]:
    """Extract repeatable answers from flat answers dict."""
    pattern = re.compile(rf"^{re.escape(question_id)}_(\d+)$")
    results = []
    for k, v in answers.items():
        if pattern.match(k):
            results.append(v)
    return results


def evaluate_rule_condition(
    condition: Union[FilterCondition, CompoundCondition],
    answers: Dict[str, Any],
    instance_idx: Optional[int] = None,
) -> bool:
    """Evaluate a condition against answers."""
    if isinstance(condition, FilterCondition):
        qid = str(condition.question_id)
        if instance_idx is not None:
            value = answers.get(f"{qid}_{instance_idx}")
        else:
            value = answers.get(qid)
        if value is None:
            return False
        if condition.operator is None:
            return False
        return evaluate_condition({condition.operator.value: condition.value}, value)
    elif isinstance(condition, CompoundCondition):
        op = condition.operator
        conds = condition.conditions
        if op == "and":
            return all(evaluate_rule_condition(c, answers, instance_idx) for c in conds)
        elif op == "or":
            return any(evaluate_rule_condition(c, answers, instance_idx) for c in conds)
        elif op == "not":
            return not evaluate_rule_condition(conds[0], answers, instance_idx)
    return False


def evaluate_scoring_rule(rule: ScoringRule, answers: Dict[str, Any]) -> Dict[str, Any]:
    """Evaluate a scoring rule."""
    qid = str(rule.question_id)
    # Repeatable: check for question_id_{i} keys
    instance_keys = [k for k in answers if k.startswith(f"{qid}_")]
    matched_answers = []
    if instance_keys:
        # Per-instance scoring (no aggregation for SCORING rules)
        for k in sorted(instance_keys, key=lambda x: int(x.split("_")[-1])):
            idx = int(k.split("_")[-1])
            match = evaluate_rule_condition(rule.condition, answers, idx)
            matched_answers.append(match)
        score = sum(1 for m in matched_answers if m) * rule.weight
        reason = f"{sum(matched_answers)} of {len(matched_answers)} instances matched"
    else:
        match = evaluate_rule_condition(rule.condition, answers)
        matched_answers = [match]
        score = rule.weight if match else 0.0
        reason = "Matched" if match else "Did not match"
    return {
        "rule_id": str(rule.id),
        "score_awarded": score,
        "matched_answers": matched_answers,
        "reason": reason,
    }


def evaluate_bonus_rule(rule: ScoringRule, answers: Dict[str, Any]) -> Dict[str, Any]:
    """Evaluate a bonus rule with aggregation support."""
    qid = str(rule.question_id) if rule.question_id else None
    aggregation = getattr(rule, "aggregation", AggregationType.NONE)
    threshold = getattr(rule, "aggregate_threshold", None)
    operator = getattr(rule, "aggregate_operator", None)
    bonus_points = rule.bonus_points or 0.0
    matched_answers = []

    # Repeatable: check for question_id_{i} keys
    if qid:
        instance_keys = [k for k in answers if k.startswith(f"{qid}_")]
        values = []
        for k in sorted(instance_keys, key=lambda x: int(x.split("_")[-1])):
            idx = int(k.split("_")[-1])
            match = evaluate_rule_condition(rule.condition, answers, idx)
            matched_answers.append(match)
            v = answers.get(k)
            values.append(v)
        total = len(matched_answers)
        count = sum(1 for m in matched_answers if m)
        pct = (count / total * 100) if total > 0 else 0
        sum_val = sum(
            float(v)
            for v, m in zip(values, matched_answers)
            if m
            and isinstance(v, (int, float, str))
            and str(v).replace(".", "", 1).isdigit()
        )

        # Aggregation logic
        if aggregation == AggregationType.ANY:
            award = bonus_points if any(matched_answers) else 0.0
            reason = f"ANY: {count} of {total} matched"
        elif aggregation == AggregationType.ALL:
            award = bonus_points if all(matched_answers) and total > 0 else 0.0
            reason = f"ALL: {count} of {total} matched"
        elif aggregation == AggregationType.COUNT:
            award = bonus_points if count >= (threshold or 1) else 0.0
            reason = f"COUNT: {count} of {total} matched (threshold: {threshold})"
        elif aggregation == AggregationType.PERCENTAGE:
            award = bonus_points if pct >= (threshold or 50) else 0.0
            reason = f"PERCENTAGE: {pct:.1f}% matched (threshold: {threshold}%)"
        elif aggregation == AggregationType.SUM:
            cmp_op = operator or ConditionOperator.GREATER_THAN_EQUALS
            cmp_val = threshold or 0.0
            if cmp_op == ConditionOperator.GREATER_THAN_EQUALS:
                ok = sum_val >= cmp_val
            elif cmp_op == ConditionOperator.GREATER_THAN:
                ok = sum_val > cmp_val
            elif cmp_op == ConditionOperator.LESS_THAN:
                ok = sum_val < cmp_val
            elif cmp_op == ConditionOperator.LESS_THAN_EQUALS:
                ok = sum_val <= cmp_val
            elif cmp_op == ConditionOperator.EQUALS:
                ok = abs(sum_val - cmp_val) < 0.01
            elif cmp_op == ConditionOperator.NOT_EQUALS:
                ok = abs(sum_val - cmp_val) >= 0.01
            else:
                ok = sum_val >= cmp_val
            award = bonus_points if ok else 0.0
            reason = f"SUM: {sum_val} {cmp_op.value if cmp_op else ''} {cmp_val}"
        else:
            award = bonus_points if any(matched_answers) else 0.0
            reason = f"Default (ANY): {count} of {total} matched"
    else:
        # Non-repeatable bonus
        match = evaluate_rule_condition(rule.condition, answers)
        matched_answers = [match]
        award = bonus_points if match else 0.0
        reason = "Matched" if match else "Did not match"
    return {
        "rule_id": str(rule.id),
        "bonus_awarded": award,
        "matched_answers": matched_answers,
        "reason": reason,
    }


def score_submission(submission: Dict[str, Any], thesis: Any) -> Dict[str, Any]:
    """Main entrypoint for scoring a submission."""
    answers = submission.get("answers", {})
    scoring_rules = getattr(thesis, "scoring_rules", [])
    total_score = 0.0
    scoring_results = []
    bonus_results = []
    for rule in scoring_rules:
        if rule.is_deleted:
            continue
        if rule.rule_type == RuleType.SCORING:
            res = evaluate_scoring_rule(rule, answers)
            total_score += res["score_awarded"]
            scoring_results.append(res)
        elif rule.rule_type == RuleType.BONUS:
            res = evaluate_bonus_rule(rule, answers)
            total_score += res["bonus_awarded"]
            bonus_results.append(res)
    return {
        "total_score": total_score,
        "scoring_rules": scoring_results,
        "bonus_rules": bonus_results,
    }
