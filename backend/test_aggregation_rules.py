#!/usr/bin/env python3
"""
Test script for normalized aggregation rules functionality.

This script tests the normalized aggregation structure where aggregation fields
are at the top level of the rule, not nested in conditions.
"""

import asyncio
import sys
import os
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.models.thesis import ScoringRule, AggregationType, RuleType, FilterCondition, ConditionOperator
from app.services.thesis.scoring import ScoringEngine


def test_aggregation_validation():
    """Test aggregation rule validation."""
    print("Testing aggregation rule validation...")
    
    # Test 1: Valid COUNT aggregation rule
    try:
        rule = ScoringRule(
            thesis_id="test_thesis",
            rule_type=RuleType.BONUS,
            question_id="founder_gender",
            section_id="founders_section",
            aggregation=AggregationType.COUNT,
            aggregate_threshold=2,
            condition=FilterCondition(
                question_id="founder_gender",
                operator=ConditionOperator.EQUALS,
                value=True
            ),
            bonus_points=5,
            weight=1.0,
            is_deleted=False
        )
        print("✓ Valid COUNT aggregation rule created successfully")
    except Exception as e:
        print(f"✗ Failed to create valid COUNT rule: {e}")
    
    # Test 2: Valid PERCENTAGE aggregation rule
    try:
        rule = ScoringRule(
            thesis_id="test_thesis",
            rule_type=RuleType.BONUS,
            question_id="founder_technical",
            section_id="founders_section",
            aggregation=AggregationType.PERCENTAGE,
            aggregate_threshold=50.0,
            condition=FilterCondition(
                question_id="founder_technical",
                operator=ConditionOperator.EQUALS,
                value=True
            ),
            bonus_points=3,
            weight=1.0,
            is_deleted=False
        )
        print("✓ Valid PERCENTAGE aggregation rule created successfully")
    except Exception as e:
        print(f"✗ Failed to create valid PERCENTAGE rule: {e}")
    
    # Test 3: Invalid rule - missing threshold for COUNT
    try:
        rule = ScoringRule(
            thesis_id="test_thesis",
            rule_type=RuleType.BONUS,
            question_id="founder_gender",
            section_id="founders_section",
            aggregation=AggregationType.COUNT,
            # Missing aggregate_threshold
            condition=FilterCondition(
                question_id="founder_gender",
                operator=ConditionOperator.EQUALS,
                value=True
            ),
            bonus_points=5,
            weight=1.0,
            is_deleted=False
        )
        print("✗ Should have failed - missing threshold for COUNT")
    except ValueError as e:
        print(f"✓ Correctly rejected rule missing threshold: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
    
    # Test 4: Invalid rule - missing section_id for aggregation
    try:
        rule = ScoringRule(
            thesis_id="test_thesis",
            rule_type=RuleType.BONUS,
            question_id="founder_gender",
            # Missing section_id
            aggregation=AggregationType.ANY,
            condition=FilterCondition(
                question_id="founder_gender",
                operator=ConditionOperator.EQUALS,
                value=True
            ),
            bonus_points=5,
            weight=1.0,
            is_deleted=False
        )
        print("✗ Should have failed - missing section_id for aggregation")
    except ValueError as e:
        print(f"✓ Correctly rejected rule missing section_id: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")

    # Test 5: Valid SUM aggregation rule
    try:
        rule = ScoringRule(
            thesis_id="test_thesis",
            rule_type=RuleType.BONUS,
            question_id="founder_equity",
            section_id="founders_section",
            aggregation=AggregationType.SUM,
            aggregate_threshold=80.0,
            aggregate_operator=ConditionOperator.GREATER_THAN_EQUALS,
            condition=FilterCondition(
                question_id="founder_equity",
                operator=ConditionOperator.GREATER_THAN,
                value=0
            ),
            bonus_points=10,
            weight=1.0,
            is_deleted=False
        )
        print("✓ Valid SUM aggregation rule created successfully")
    except Exception as e:
        print(f"✗ Failed to create valid SUM rule: {e}")

    # Test 6: Invalid SUM rule - missing aggregate_operator
    try:
        rule = ScoringRule(
            thesis_id="test_thesis",
            rule_type=RuleType.BONUS,
            question_id="founder_equity",
            section_id="founders_section",
            aggregation=AggregationType.SUM,
            aggregate_threshold=80.0,
            # Missing aggregate_operator
            condition=FilterCondition(
                question_id="founder_equity",
                operator=ConditionOperator.GREATER_THAN,
                value=0
            ),
            bonus_points=10,
            weight=1.0,
            is_deleted=False
        )
        print("✗ Should have failed - missing aggregate_operator for SUM")
    except ValueError as e:
        print(f"✓ Correctly rejected SUM rule missing operator: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")

    # Test 7: Test that nested aggregation fields are rejected
    try:
        # This should fail because aggregation fields are in the condition
        rule = ScoringRule(
            thesis_id="test_thesis",
            rule_type=RuleType.BONUS,
            question_id="founder_gender",
            condition={
                "question_id": "founder_gender",
                "operator": "equals",
                "value": True,
                "aggregation": "any",  # This should be rejected
                "section_id": "founders_section"  # This should be rejected
            },
            bonus_points=5,
            weight=1.0,
            is_deleted=False
        )
        print("✗ Should have failed - aggregation fields nested in condition")
    except ValueError as e:
        print(f"✓ Correctly rejected nested aggregation fields: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")


async def test_aggregation_scoring():
    """Test aggregation scoring logic."""
    print("\nTesting aggregation scoring logic...")
    
    # Mock form responses for testing
    form_responses = {
        "repeatable_answers": {
            "founders_section": {
                "0": {"founder_gender": True, "founder_technical": True, "founder_equity": 40.0},
                "1": {"founder_gender": False, "founder_technical": True, "founder_equity": 30.0},
                "2": {"founder_gender": True, "founder_technical": False, "founder_equity": 20.0}
            }
        }
    }
    
    scoring_engine = ScoringEngine()
    
    # Test ANY aggregation
    any_rule = ScoringRule(
        thesis_id="test_thesis",
        rule_type=RuleType.BONUS,
        question_id="founder_gender",
        section_id="founders_section",
        aggregation=AggregationType.ANY,
        condition=FilterCondition(
            question_id="founder_gender",
            operator=ConditionOperator.EQUALS,
            value=True
        ),
        bonus_points=5,
        weight=1.0,
        is_deleted=False
    )
    
    result = await scoring_engine._calculate_aggregated_score(any_rule, form_responses)
    expected_score = 1.0  # Should match because at least one founder is female
    if result["score"] == expected_score:
        print(f"✓ ANY aggregation: {result['explanation']}")
    else:
        print(f"✗ ANY aggregation failed: expected {expected_score}, got {result['score']}")
    
    # Test ALL aggregation
    all_rule = ScoringRule(
        thesis_id="test_thesis",
        rule_type=RuleType.BONUS,
        question_id="founder_gender",
        section_id="founders_section",
        aggregation=AggregationType.ALL,
        condition=FilterCondition(
            question_id="founder_gender",
            operator=ConditionOperator.EQUALS,
            value=True
        ),
        bonus_points=5,
        weight=1.0,
        is_deleted=False
    )
    
    result = await scoring_engine._calculate_aggregated_score(all_rule, form_responses)
    expected_score = 0.0  # Should not match because not all founders are female
    if result["score"] == expected_score:
        print(f"✓ ALL aggregation: {result['explanation']}")
    else:
        print(f"✗ ALL aggregation failed: expected {expected_score}, got {result['score']}")
    
    # Test COUNT aggregation
    count_rule = ScoringRule(
        thesis_id="test_thesis",
        rule_type=RuleType.BONUS,
        question_id="founder_gender",
        section_id="founders_section",
        aggregation=AggregationType.COUNT,
        aggregate_threshold=2,
        condition=FilterCondition(
            question_id="founder_gender",
            operator=ConditionOperator.EQUALS,
            value=True
        ),
        bonus_points=5,
        weight=1.0,
        is_deleted=False
    )
    
    result = await scoring_engine._calculate_aggregated_score(count_rule, form_responses)
    expected_score = 1.0  # Should match because exactly 2 founders are female (>= threshold)
    if result["score"] == expected_score:
        print(f"✓ COUNT aggregation: {result['explanation']}")
    else:
        print(f"✗ COUNT aggregation failed: expected {expected_score}, got {result['score']}")
    
    # Test PERCENTAGE aggregation
    percentage_rule = ScoringRule(
        thesis_id="test_thesis",
        rule_type=RuleType.BONUS,
        question_id="founder_technical",
        section_id="founders_section",
        aggregation=AggregationType.PERCENTAGE,
        aggregate_threshold=60.0,
        condition=FilterCondition(
            question_id="founder_technical",
            operator=ConditionOperator.EQUALS,
            value=True
        ),
        bonus_points=3,
        weight=1.0,
        is_deleted=False
    )
    
    result = await scoring_engine._calculate_aggregated_score(percentage_rule, form_responses)
    expected_score = 1.0  # Should match because 66.7% of founders are technical (>= 60%)
    if result["score"] == expected_score:
        print(f"✓ PERCENTAGE aggregation: {result['explanation']}")
    else:
        print(f"✗ PERCENTAGE aggregation failed: expected {expected_score}, got {result['score']}")

    # Test SUM aggregation
    sum_rule = ScoringRule(
        thesis_id="test_thesis",
        rule_type=RuleType.BONUS,
        question_id="founder_equity",
        section_id="founders_section",
        aggregation=AggregationType.SUM,
        aggregate_threshold=80.0,
        aggregate_operator=ConditionOperator.GREATER_THAN_EQUALS,
        condition=FilterCondition(
            question_id="founder_equity",
            operator=ConditionOperator.GREATER_THAN,
            value=0
        ),
        bonus_points=10,
        weight=1.0,
        is_deleted=False
    )

    result = await scoring_engine._calculate_aggregated_score(sum_rule, form_responses)
    expected_score = 1.0  # Should match because total equity is 90% (>= 80%)
    if result["score"] == expected_score:
        print(f"✓ SUM aggregation: {result['explanation']}")
    else:
        print(f"✗ SUM aggregation failed: expected {expected_score}, got {result['score']}")


async def main():
    """Run all tests."""
    print("🧪 Testing Aggregation Rules Implementation\n")
    print("=" * 50)
    
    # Test validation
    test_aggregation_validation()
    
    # Test scoring logic
    await test_aggregation_scoring()
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
