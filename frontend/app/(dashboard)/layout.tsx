"use client"

import Link from "next/link"
import { dashboardConfig } from "@/config/dashboard"
import { MainNav } from "@/components/main-nav"
import { SiteFooter } from "@/components/site-footer"
import { UserAccountNav } from "@/components/user-account-nav"
import { ProtectedRoute } from "@/components/protected-route"
import { OrganizationSelector } from "@/components/org-selector"
import { EnhancedSidebar } from "@/components/enhanced-sidebar"
import { useAuth } from "@/lib/auth-context"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"
import { Home, FileText, BarChart3, Target, Settings } from "lucide-react"
import { Icons } from "@/components/icons"

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const [mounted, setMounted] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Handle hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className="flex h-screen w-full items-center justify-center flex-1">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return <DashboardLayoutContent 
    sidebarOpen={sidebarOpen}
    setSidebarOpen={setSidebarOpen}
    mobileMenuOpen={mobileMenuOpen}
    setMobileMenuOpen={setMobileMenuOpen}
  >
    {children}
  </DashboardLayoutContent>;
}

function DashboardLayoutContent({
  children,
  sidebarOpen,
  setSidebarOpen,
  mobileMenuOpen,
  setMobileMenuOpen,
}: {
  children: React.ReactNode;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean | ((prev: boolean) => boolean)) => void;
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
}) {
  const { user } = useAuth();

  return (
    <ProtectedRoute>
      {/* Top-level Layout: Horizontal Flex Row - Full Screen, No Gaps */}
      <div className="flex h-screen w-full overflow-hidden flex-1">
        {/* Vertical Sidebar - Always Left, Full Height */}
        <aside className={cn(
          "flex flex-col h-full shrink-0",
          // Desktop: fixed width, Mobile: hidden (will show as overlay)
          "hidden md:flex",
          sidebarOpen ? "w-[260px]" : "w-[72px]"
        )}>
          <EnhancedSidebar
            items={dashboardConfig.sidebarNav}
            isCollapsed={!sidebarOpen}
            onToggleCollapse={() => setSidebarOpen(!sidebarOpen)}
          />
        </aside>

        {/* Main Content Area - Fill Remaining Space */}
        <div className="flex flex-col flex-1 h-full overflow-hidden min-w-0">
          {/* Header - No Margins */}
          <header className={cn(
            "flex-shrink-0 border-b bg-background z-30"
          )}>
            <div className="flex h-16 items-center justify-between px-4 md:px-6 lg:px-8">
              <div className="flex items-center gap-3">
                {/* Mobile Menu Button */}
                <button
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                  className={cn(
                    "md:hidden rounded-xl p-2 transition-all duration-200",
                    "hover:bg-accent active:scale-95 touch-target",
                    "focus:outline-none focus:ring-2 focus:ring-ring"
                  )}
                  aria-label="Toggle mobile menu"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d={mobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                    />
                  </svg>
                </button>
                {/* Desktop Navigation */}
                <div className="hidden md:flex items-center gap-4">
                  <MainNav items={dashboardConfig.mainNav} />
                  <OrganizationSelector />
                </div>

                {/* Mobile: Show org selector only */}
                <div className="md:hidden">
                  <OrganizationSelector />
                </div>
              </div>

              {/* User Account Navigation */}
              {user && (
                <UserAccountNav
                  user={{
                    name: user.name,
                    image: null,
                    email: user.email,
                  }}
                />
              )}
            </div>
          </header>
          {/* Mobile Sidebar Overlay */}
          {mobileMenuOpen && (
            <div className="md:hidden fixed inset-0 z-40">
              <div
                className="absolute inset-0 bg-black/50 backdrop-blur-sm"
                onClick={() => setMobileMenuOpen(false)}
              />
              <div className="absolute inset-y-0 left-0 w-full max-w-sm bg-background border-r shadow-xl safe-top safe-bottom">
                <div className="flex items-center justify-between p-6 border-b">
                  <div className="flex items-center">
                    <Icons.logo className="h-8 text-foreground" />
                  </div>
                  <button
                    onClick={() => setMobileMenuOpen(false)}
                    className="rounded-xl p-2 hover:bg-accent active:scale-95 touch-target"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <nav className="flex-1 overflow-y-auto p-6">
                  <div className="space-y-3">
                    {dashboardConfig.sidebarNav.map((item, index) => {
                      const iconMap = {
                        dashboard: Home,
                        form: FileText,
                        post: BarChart3,
                        page: Target,
                        settings: Settings,
                      } as const;
                      
                      const iconKey = item.icon as keyof typeof iconMap;
                      const Icon = iconMap[iconKey] || Home;
                      
                      return (
                        <Link
                          key={index}
                          href={item.href || '#'}
                          className="flex items-center gap-4 p-4 rounded-xl transition-all duration-200 hover:bg-accent hover:text-accent-foreground active:scale-[0.98] touch-target text-lg font-medium"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          <div className="flex items-center justify-center w-6 h-6">
                            <Icon className="w-5 h-5 text-muted-foreground" />
                          </div>
                          <span className="text-lg font-medium">{item.title}</span>
                        </Link>
                      );
                    })}
                  </div>
                </nav>
              </div>
            </div>
          )}

          {/* Main Content - Fill Remaining Height */}
          <main className="flex-1 overflow-y-auto overflow-x-hidden px-4 py-6 md:px-6 lg:px-8 min-w-0">
            {children}
          </main>

          {/* Footer - No Margins */}
          <SiteFooter className="flex-shrink-0 border-t" />
        </div>
      </div>
    </ProtectedRoute>
  )
}
