"use client"

import React, { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence, useDragControls, PanInfo } from "framer-motion"
import { MessageCircle, X, Minimize2, Maximize2, G<PERSON>Horizontal } from "lucide-react"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/auth-context"
import { visualRetreat } from "@/lib/utils/responsive"

// Types for Orbit Assistant
interface OrbitPosition {
  x: number
  y: number
}

interface OrbitSize {
  width: number
  height: number
  mode: 'compact' | 'normal' | 'expanded'
}

interface OrbitState {
  isOpen: boolean
  position: OrbitPosition
  size: OrbitSize
  activeTab: 'chat' | 'research' | 'agent'
}

// Default positions and sizes
const DEFAULT_POSITION: OrbitPosition = {
  x: typeof window !== 'undefined' ? window.innerWidth - 80 : 80,
  y: typeof window !== 'undefined' ? window.innerHeight - 80 : 80
}

const DEFAULT_MOBILE_POSITION: OrbitPosition = {
  x: typeof window !== 'undefined' ? window.innerWidth / 2 - 30 : 30,
  y: typeof window !== 'undefined' ? window.innerHeight - 80 : 80
}

const SIZES: Record<OrbitSize['mode'], Omit<OrbitSize, 'mode'>> = {
  compact: { width: 320, height: 400 },
  normal: { width: 480, height: 600 },
  expanded: { width: 640, height: 800 }
}

// Storage keys
const STORAGE_KEYS = {
  position: 'orbit-position',
  size: 'orbit-size',
  isOpen: 'orbit-is-open',
  activeTab: 'orbit-active-tab'
}

// Utility functions
const isMobile = () => typeof window !== 'undefined' && window.innerWidth < 768
const getStorageKey = (key: string, orgId?: string) => 
  orgId ? `${key}-${orgId}` : key

const getSafePosition = (position: OrbitPosition, size: OrbitSize): OrbitPosition => {
  if (typeof window === 'undefined') return position

  const padding = 16
  const navHeight = 64 // Account for navigation bars
  const maxX = window.innerWidth - size.width - padding
  const maxY = window.innerHeight - size.height - padding

  return {
    x: Math.max(padding, Math.min(position.x, maxX)),
    y: Math.max(navHeight, Math.min(position.y, maxY))
  }
}

// Enhanced safe position with snap zones
const getSafePositionWithSnap = (position: OrbitPosition, size: OrbitSize): OrbitPosition => {
  if (typeof window === 'undefined') return position

  const padding = 16
  const snapThreshold = 30
  const navHeight = 64
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let { x, y } = position

  // Snap to edges
  if (x < snapThreshold) x = padding
  if (x > viewportWidth - size.width - snapThreshold) x = viewportWidth - size.width - padding
  if (y < navHeight + snapThreshold) y = navHeight
  if (y > viewportHeight - size.height - snapThreshold) y = viewportHeight - size.height - padding

  // Ensure within bounds
  x = Math.max(padding, Math.min(x, viewportWidth - size.width - padding))
  y = Math.max(navHeight, Math.min(y, viewportHeight - size.height - padding))

  return { x, y }
}

const getDefaultPosition = (): OrbitPosition => {
  if (typeof window === 'undefined') return { x: 80, y: 80 }
  
  return isMobile() ? {
    x: window.innerWidth / 2 - 30,
    y: window.innerHeight - 80
  } : {
    x: window.innerWidth - 80,
    y: window.innerHeight - 80
  }
}

// Orbit Icon Component
interface OrbitIconProps {
  onClick: () => void
  position: OrbitPosition
  isDragging: boolean
  onDragStart: () => void
  onDragEnd: (position: OrbitPosition) => void
}

function OrbitIcon({ onClick, position, isDragging, onDragStart, onDragEnd }: OrbitIconProps) {
  const [dragStarted, setDragStarted] = useState(false)
  const [localPosition, setLocalPosition] = useState(position)

  // Update local position when prop changes (but not during drag)
  useEffect(() => {
    if (!isDragging) {
      setLocalPosition(position)
    }
  }, [position, isDragging])

  const handleDragStart = () => {
    console.log('🎯 Icon drag started')
    setDragStarted(true)
    onDragStart()
  }

  const handleDrag = (event: any, info: PanInfo) => {
    // Update local position during drag for smooth movement
    const newPosition = {
      x: localPosition.x + info.delta.x,
      y: localPosition.y + info.delta.y
    }
    setLocalPosition(newPosition)
  }

  const handleDragEnd = (event: any, info: PanInfo) => {
    const finalPosition = {
      x: localPosition.x,
      y: localPosition.y
    }

    console.log('🎯 Icon drag ended:', finalPosition)

    // Apply safe position constraints
    const iconSize = { width: 56, height: 56, mode: 'compact' as const }
    const safePosition = getSafePositionWithSnap(finalPosition, iconSize)

    console.log('🎯 Safe position:', safePosition)

    setLocalPosition(safePosition)
    onDragEnd(safePosition)
    setDragStarted(false)
  }

  const handleClick = () => {
    // Only trigger click if we didn't drag
    if (!dragStarted) {
      onClick()
    }
  }

  // Get drag constraints
  const getDragConstraints = () => {
    if (typeof window === 'undefined') return {}

    const iconSize = 56
    const padding = 16

    if (isMobile()) {
      return {
        left: 0,
        right: 0,
        top: 64,
        bottom: window.innerHeight - iconSize - padding
      }
    }

    return {
      left: padding,
      right: window.innerWidth - iconSize - padding,
      top: 64,
      bottom: window.innerHeight - iconSize - padding
    }
  }

  return (
    <motion.div
      drag={isMobile() ? "y" : true}
      dragMomentum={false}
      dragElastic={0}
      dragConstraints={getDragConstraints()}
      onDragStart={handleDragStart}
      onDrag={handleDrag}
      onDragEnd={handleDragEnd}
      animate={{
        x: localPosition.x,
        y: localPosition.y,
        scale: isDragging ? 1.1 : 1,
        boxShadow: isDragging
          ? "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
          : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
      }}
      transition={{
        type: "spring",
        damping: isDragging ? 50 : 25,
        stiffness: isDragging ? 300 : 200
      }}
      style={{
        position: 'fixed',
        zIndex: 9998,
        willChange: 'transform',
        pointerEvents: 'auto'
      }}
      className="touch-manipulation"
      whileHover={!isMobile() ? { scale: 1.05 } : {}}
      whileTap={{ scale: 0.95 }}
    >
      <button
        onClick={handleClick}
        className={cn(
          "relative rounded-full",
          "bg-primary/90 hover:bg-primary text-white",
          "backdrop-blur-md border-0 shadow-lg hover:shadow-xl",
          "transition-all duration-200",
          "flex items-center justify-center group",
          // Mobile: larger touch target
          isMobile() ? "h-16 w-16 touch-target-lg" : "h-14 w-14 touch-target"
        )}
        aria-label="Open Orbit AI Assistant"
        aria-describedby="orbit-tooltip"
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            handleClick()
          }
        }}
      >
        <MessageCircle className="h-6 w-6" />

        {/* Pulse animation */}
        <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping" />

        {/* Tooltip */}
        <motion.div
          initial={{ opacity: 0, x: 10 }}
          whileHover={{ opacity: 1, x: 0 }}
          className="absolute right-full mr-3 top-1/2 -translate-y-1/2 pointer-events-none"
        >
          <div
            id="orbit-tooltip"
            className="bg-gray-900 text-white text-sm px-3 py-1.5 rounded-lg shadow-lg whitespace-nowrap"
            role="tooltip"
            aria-hidden="true"
          >
            Ask Orbit
            <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900" />
          </div>
        </motion.div>
      </button>
    </motion.div>
  )
}

// Orbit Modal Component
interface OrbitModalProps {
  state: OrbitState
  setState: React.Dispatch<React.SetStateAction<OrbitState>>
  saveState: (newState: Partial<OrbitState>) => void
  onClose: () => void
}

function OrbitModal({ state, setState, saveState, onClose }: OrbitModalProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [swipeOffset, setSwipeOffset] = useState(0)
  const [localPosition, setLocalPosition] = useState(state.position)
  const modalRef = useRef<HTMLDivElement>(null)

  // Update local position when state changes (but not during drag)
  useEffect(() => {
    if (!isDragging) {
      setLocalPosition(state.position)
    }
  }, [state.position, isDragging])

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }

      // Tab navigation between tabs
      if (event.key === 'Tab' && event.ctrlKey) {
        event.preventDefault()
        const tabs: OrbitState['activeTab'][] = ['chat', 'research', 'agent']
        const currentIndex = tabs.indexOf(state.activeTab)
        const nextIndex = event.shiftKey
          ? (currentIndex - 1 + tabs.length) % tabs.length
          : (currentIndex + 1) % tabs.length
        handleTabChange(tabs[nextIndex])
      }

      // Arrow key navigation for resize (desktop only)
      if (!isMobile() && event.altKey) {
        if (event.key === 'ArrowLeft' || event.key === 'ArrowDown') {
          event.preventDefault()
          const modes: OrbitSize['mode'][] = ['compact', 'normal', 'expanded']
          const currentIndex = modes.indexOf(state.size.mode)
          if (currentIndex > 0) {
            handleResize(modes[currentIndex - 1])
          }
        } else if (event.key === 'ArrowRight' || event.key === 'ArrowUp') {
          event.preventDefault()
          const modes: OrbitSize['mode'][] = ['compact', 'normal', 'expanded']
          const currentIndex = modes.indexOf(state.size.mode)
          if (currentIndex < modes.length - 1) {
            handleResize(modes[currentIndex + 1])
          }
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onClose, state.activeTab, state.size.mode, handleTabChange, handleResize])

  // Focus management and focus trap
  useEffect(() => {
    if (modalRef.current) {
      modalRef.current.focus()
    }

    // Focus trap
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return

      const modal = modalRef.current
      if (!modal) return

      const focusableElements = modal.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
      const firstElement = focusableElements[0] as HTMLElement
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement.focus()
        }
      }
    }

    document.addEventListener('keydown', handleTabKey)
    return () => document.removeEventListener('keydown', handleTabKey)
  }, [])

  // Handle drag start
  const handleDragStart = useCallback(() => {
    setIsDragging(true)
  }, [])

  // Handle drag during movement
  const handleDrag = useCallback((event: any, info: PanInfo) => {
    if (isMobile()) {
      // Update swipe offset for visual feedback
      setSwipeOffset(Math.max(0, info.offset.y))
    } else {
      // Update local position during drag for smooth movement
      const newPosition = {
        x: localPosition.x + info.delta.x,
        y: localPosition.y + info.delta.y
      }
      setLocalPosition(newPosition)
    }
  }, [localPosition])

  // Handle drag end
  const handleDragEnd = useCallback((event: any, info: PanInfo) => {
    if (isMobile()) {
      // Handle swipe-to-close on mobile
      const swipeThreshold = 100
      if (swipeOffset > swipeThreshold) {
        onClose()
        return
      }
      setSwipeOffset(0)
    } else {
      // Handle position drag on desktop
      const safePosition = getSafePositionWithSnap(localPosition, state.size)

      setLocalPosition(safePosition)
      setState(prev => ({ ...prev, position: safePosition }))
      saveState({ position: safePosition }, true) // Immediate save
    }

    setIsDragging(false)
  }, [localPosition, state.size, setState, saveState, onClose, swipeOffset])

  // Handle resize
  const handleResize = useCallback((mode: OrbitSize['mode']) => {
    const newSize = { ...SIZES[mode], mode }
    const safePosition = getSafePositionWithSnap(state.position, newSize)

    setState(prev => ({
      ...prev,
      size: newSize,
      position: safePosition
    }))
    saveState({ size: newSize, position: safePosition })
  }, [state.position, setState, saveState])

  // Get resize button props for better UX
  const getResizeButtonProps = (mode: OrbitSize['mode']) => ({
    onClick: () => handleResize(mode),
    className: cn(
      "p-1.5 rounded-lg transition-all duration-200 touch-target",
      "hover:bg-gray-100 hover:scale-105 active:scale-95",
      "focus:outline-none focus:ring-2 focus:ring-primary/20",
      state.size.mode === mode
        ? "bg-primary/10 text-primary ring-2 ring-primary/20"
        : "text-gray-600 hover:text-gray-900"
    ),
    'aria-label': `${mode.charAt(0).toUpperCase() + mode.slice(1)} size`,
    'aria-pressed': state.size.mode === mode
  })

  // Handle tab change
  const handleTabChange = useCallback((tab: OrbitState['activeTab']) => {
    setState(prev => ({ ...prev, activeTab: tab }))
    saveState({ activeTab: tab })
  }, [setState, saveState])

  // Get modal position - ensure it never clips or opens under content
  const getModalPosition = () => {
    if (isMobile()) {
      return {
        position: 'fixed' as const,
        bottom: 0,
        left: 0,
        right: 0,
        top: 'auto',
        zIndex: 10000
      }
    }

    // Desktop: use local position for smooth dragging
    const currentPosition = isDragging ? localPosition : state.position

    return {
      position: 'fixed' as const,
      left: currentPosition.x,
      top: currentPosition.y,
      width: state.size.width,
      height: state.size.height,
      zIndex: 10000
    }
  }

  return (
    <>
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-[9999] bg-black/50 backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <motion.div
        ref={modalRef}
        drag={isMobile() ? "y" : true}
        dragMomentum={false}
        dragElastic={0}
        dragConstraints={isMobile() ? {
          top: 0,
          bottom: 200 // Allow swipe down to close
        } : {
          left: 16,
          right: window?.innerWidth ? window.innerWidth - state.size.width - 16 : 0,
          top: 64,
          bottom: window?.innerHeight ? window.innerHeight - state.size.height - 16 : 0
        }}
        onDragStart={handleDragStart}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        initial={{
          opacity: 0,
          scale: 0.95,
          y: isMobile() ? 100 : 0
        }}
        animate={{
          opacity: Math.max(0.3, 1 - swipeOffset / 200), // Fade out during swipe
          scale: isDragging ? 1.02 : 1,
          y: isMobile() ? swipeOffset : 0,
          boxShadow: isDragging
            ? "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
            : "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
        }}
        exit={{
          opacity: 0,
          scale: 0.95,
          y: isMobile() ? 100 : 0
        }}
        transition={{
          type: "spring",
          damping: isDragging ? 50 : 25,
          stiffness: isDragging ? 300 : 200
        }}
        style={{
          ...getModalPosition(),
          willChange: 'transform',
          pointerEvents: 'auto'
        }}
        className={cn(
          "flex flex-col outline-none",
          "bg-white/95 backdrop-blur-lg",
          "border border-gray-200/50",
          "touch-manipulation",
          // Mobile: bottom sheet
          isMobile() && [
            "rounded-t-2xl max-h-[90vh]",
            "mx-4 mb-4 safe-bottom"
          ],
          // Desktop: floating modal
          !isMobile() && [
            "rounded-2xl max-h-[85vh]"
          ]
        )}
        tabIndex={-1}
        role="dialog"
        aria-modal="true"
        aria-labelledby="orbit-title"
        aria-describedby="orbit-description"
      >
        {/* Mobile Swipe Indicator */}
        {isMobile() && (
          <div className="flex justify-center pt-2 pb-1">
            <div className="w-8 h-1 bg-gray-300 rounded-full" />
          </div>
        )}

        {/* Header */}
        <div
          className={cn(
            "flex items-center justify-between p-4 border-b border-gray-200/50",
            "touch-manipulation",
            !isMobile() && "cursor-move",
            isMobile() && "cursor-default"
          )}
          style={{
            pointerEvents: 'auto',
            touchAction: isMobile() ? 'pan-y' : 'none'
          }}
        >
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
              <MessageCircle className="h-4 w-4 text-primary" />
            </div>
            <h2 id="orbit-title" className="text-lg font-semibold text-gray-900">Orbit AI</h2>
            <p id="orbit-description" className="sr-only">
              AI assistant for chat, research, and automated tasks. Use Tab to navigate, Escape to close.
            </p>
          </div>

          <div className="flex items-center gap-2">
            {/* Resize Controls - Desktop only */}
            {!isMobile() && (
              <div className="flex items-center gap-1">
                <motion.button
                  {...getResizeButtonProps('compact')}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Minimize2 className="h-4 w-4" />
                </motion.button>
                <motion.button
                  {...getResizeButtonProps('normal')}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <GripHorizontal className="h-4 w-4" />
                </motion.button>
                <motion.button
                  {...getResizeButtonProps('expanded')}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Maximize2 className="h-4 w-4" />
                </motion.button>
              </div>
            )}

            {/* Close Button */}
            <button
              onClick={onClose}
              className={cn(
                "p-1.5 rounded-lg transition-all duration-200 touch-target",
                "hover:bg-gray-100 focus:bg-gray-100",
                "focus:outline-none focus:ring-2 focus:ring-primary/20",
                "active:scale-95"
              )}
              aria-label="Close Orbit AI Assistant"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="relative border-b border-gray-200/50" role="tablist" aria-label="Orbit AI modes">
          <div className="flex px-4">
            {(['chat', 'research', 'agent'] as const).map((tab, index) => (
              <button
                key={tab}
                onClick={() => handleTabChange(tab)}
                className={cn(
                  "relative px-4 py-3 text-sm font-medium transition-all duration-200 touch-target",
                  "hover:bg-gray-50/50 rounded-t-lg",
                  state.activeTab === tab
                    ? "text-primary"
                    : "text-gray-600 hover:text-gray-900"
                )}
                role="tab"
                aria-selected={state.activeTab === tab}
                aria-controls={`orbit-panel-${tab}`}
                id={`orbit-tab-${tab}`}
                tabIndex={state.activeTab === tab ? 0 : -1}
                onKeyDown={(e) => {
                  if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    e.preventDefault()
                    const tabs = ['chat', 'research', 'agent'] as const
                    const currentIndex = tabs.indexOf(tab)
                    const nextIndex = e.key === 'ArrowLeft'
                      ? (currentIndex - 1 + tabs.length) % tabs.length
                      : (currentIndex + 1) % tabs.length
                    handleTabChange(tabs[nextIndex])
                  }
                }}
              >
                <span className="relative z-10">
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </span>

                {/* Active indicator */}
                {state.activeTab === tab && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
                    transition={{ duration: 0.2, ease: "easeOut" }}
                  />
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div
          className="flex-1 overflow-hidden"
          role="tabpanel"
          id={`orbit-panel-${state.activeTab}`}
          aria-labelledby={`orbit-tab-${state.activeTab}`}
        >
          <OrbitContent activeTab={state.activeTab} size={state.size} />
        </div>
      </motion.div>
    </>
  )
}

// Orbit Content Component
interface OrbitContentProps {
  activeTab: OrbitState['activeTab']
  size: OrbitSize
}

function OrbitContent({ activeTab, size }: OrbitContentProps) {
  const [chatInput, setChatInput] = useState('')
  const [messages, setMessages] = useState<Array<{id: string, text: string, sender: 'user' | 'orbit'}>>([])

  const handleSendMessage = () => {
    if (!chatInput.trim()) return

    const userMessage = {
      id: Date.now().toString(),
      text: chatInput,
      sender: 'user' as const
    }

    setMessages(prev => [...prev, userMessage])
    setChatInput('')

    // Simulate AI response
    setTimeout(() => {
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        text: "I'm Orbit AI, your intelligent assistant. I'm currently in development, but I'll soon be able to help you with research, analysis, and task automation.",
        sender: 'orbit' as const
      }
      setMessages(prev => [...prev, aiMessage])
    }, 1000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <div className="h-full flex flex-col">
            {/* Chat Messages */}
            <div
              className="flex-1 overflow-y-auto p-4 space-y-4"
              role="log"
              aria-live="polite"
              aria-label="Chat conversation"
            >
              {messages.length === 0 ? (
                <div className="text-center text-gray-500 mt-8">
                  <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-sm">Start a conversation with Orbit AI</p>
                  <p className="text-xs text-gray-400 mt-2">
                    Ask questions, get insights, or request analysis
                  </p>
                </div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex",
                      message.sender === 'user' ? "justify-end" : "justify-start"
                    )}
                    role="article"
                    aria-label={`${message.sender === 'user' ? 'You' : 'Orbit AI'} said`}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] px-3 py-2 rounded-lg text-sm",
                        message.sender === 'user'
                          ? "bg-primary text-white"
                          : "bg-gray-100 text-gray-900"
                      )}
                    >
                      {message.text}
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Chat Input */}
            <div className="border-t border-gray-200/50 p-4">
              <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }}>
                <div className="flex gap-2">
                  <label htmlFor="chat-input" className="sr-only">
                    Message to Orbit AI
                  </label>
                  <input
                    id="chat-input"
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Ask Orbit anything..."
                    className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                    aria-describedby="chat-input-help"
                  />
                  <div id="chat-input-help" className="sr-only">
                    Type your message and press Enter or click Send to chat with Orbit AI
                  </div>
                  <button
                    type="submit"
                    onClick={handleSendMessage}
                    disabled={!chatInput.trim()}
                    className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                    aria-label="Send message to Orbit AI"
                  >
                    Send
                  </button>
                </div>
              </form>
            </div>
          </div>
        )

      case 'research':
        return (
          <div className="p-4 h-full flex flex-col">
            <div className="text-center text-gray-500 mt-8 mb-8">
              <div className="h-12 w-12 mx-auto mb-4 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-lg">R</span>
              </div>
              <p className="text-sm font-medium">Research Mode</p>
              <p className="text-xs text-gray-400 mt-2">
                Deep analysis and market insights
              </p>
            </div>

            {/* Research Features Preview */}
            <div className="space-y-3 flex-1">
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-1">Market Analysis</h4>
                <p className="text-xs text-gray-600">Comprehensive market research and competitive analysis</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-1">Data Insights</h4>
                <p className="text-xs text-gray-600">Extract insights from your data and documents</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-1">Trend Monitoring</h4>
                <p className="text-xs text-gray-600">Track industry trends and emerging opportunities</p>
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-xs text-blue-700 text-center">
                Research mode will be available in the next update
              </p>
            </div>
          </div>
        )

      case 'agent':
        return (
          <div className="p-4 h-full flex flex-col">
            <div className="text-center text-gray-500 mt-8 mb-8">
              <div className="h-12 w-12 mx-auto mb-4 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 font-semibold text-lg">A</span>
              </div>
              <p className="text-sm font-medium">Agent Mode</p>
              <p className="text-xs text-gray-400 mt-2">
                Autonomous task execution and workflow automation
              </p>
            </div>

            {/* Agent Features Preview */}
            <div className="space-y-3 flex-1">
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-1">Task Automation</h4>
                <p className="text-xs text-gray-600">Automate repetitive tasks and workflows</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-1">Smart Scheduling</h4>
                <p className="text-xs text-gray-600">Intelligent calendar and meeting management</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-1">Data Processing</h4>
                <p className="text-xs text-gray-600">Automated data entry and report generation</p>
              </div>
            </div>

            <div className="mt-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
              <p className="text-xs text-purple-700 text-center">
                Agent mode will be available in the next update
              </p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="h-full overflow-hidden">
      {renderContent()}
    </div>
  )
}

// Main Orbit Assistant Component
interface OrbitAssistantProps {
  className?: string
}

export function OrbitAssistant({ className }: OrbitAssistantProps) {
  const { user, orgId } = useAuth()
  const [mounted, setMounted] = useState(false)
  const [isDragging, setIsDragging] = useState(false)

  // Orbit state
  const [state, setState] = useState<OrbitState>({
    isOpen: false,
    position: getDefaultPosition(),
    size: { ...SIZES.normal, mode: 'normal' },
    activeTab: 'chat'
  })

  // Debounced save to prevent excessive localStorage writes
  const debouncedSave = useRef<NodeJS.Timeout>()

  // Save state to localStorage with error handling and quota management
  const saveState = useCallback((newState: Partial<OrbitState>, immediate = false) => {
    if (typeof window === 'undefined' || !orgId) return

    const doSave = () => {
      try {
        // Check if localStorage is available and has space
        const testKey = '__orbit_test__'
        localStorage.setItem(testKey, 'test')
        localStorage.removeItem(testKey)

        if (newState.position) {
          const positionKey = getStorageKey(STORAGE_KEYS.position, orgId)
          localStorage.setItem(positionKey, JSON.stringify(newState.position))
        }

        if (newState.size) {
          const sizeKey = getStorageKey(STORAGE_KEYS.size, orgId)
          localStorage.setItem(sizeKey, JSON.stringify(newState.size))
        }

        if (newState.isOpen !== undefined) {
          const isOpenKey = getStorageKey(STORAGE_KEYS.isOpen, orgId)
          localStorage.setItem(isOpenKey, JSON.stringify(newState.isOpen))
        }

        if (newState.activeTab) {
          const activeTabKey = getStorageKey(STORAGE_KEYS.activeTab, orgId)
          localStorage.setItem(activeTabKey, newState.activeTab)
        }
      } catch (error) {
        if (error instanceof DOMException && error.code === 22) {
          // Storage quota exceeded
          console.warn('localStorage quota exceeded, clearing old Orbit data')
          try {
            // Clear old orbit data for other orgs to make space
            const keysToRemove: string[] = []
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i)
              if (key && key.startsWith('orbit-') && !key.includes(orgId)) {
                keysToRemove.push(key)
              }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key))

            // Try saving again
            doSave()
          } catch (retryError) {
            console.warn('Failed to save Orbit state after cleanup:', retryError)
          }
        } else {
          console.warn('Failed to save Orbit state to localStorage:', error)
        }
      }
    }

    if (immediate) {
      // Clear any pending debounced save
      if (debouncedSave.current) {
        clearTimeout(debouncedSave.current)
      }
      doSave()
    } else {
      // Debounce saves during drag operations
      if (debouncedSave.current) {
        clearTimeout(debouncedSave.current)
      }
      debouncedSave.current = setTimeout(doSave, 100)
    }
  }, [orgId])

  // Handle mounting
  useEffect(() => {
    setMounted(true)
    loadPersistedState()
  }, [orgId])

  // Handle viewport changes with enhanced recovery
  useEffect(() => {
    if (!mounted) return

    let resizeTimeout: NodeJS.Timeout

    const handleResize = () => {
      // Debounce resize events
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        setState(prev => {
          const currentViewport = {
            width: window.innerWidth,
            height: window.innerHeight
          }

          // Check if position is completely off-screen
          const isOffScreen =
            prev.position.x > currentViewport.width ||
            prev.position.y > currentViewport.height ||
            prev.position.x + prev.size.width < 0 ||
            prev.position.y + prev.size.height < 0

          let newPosition = prev.position
          let newSize = prev.size

          if (isOffScreen) {
            // Reset to default position if completely off-screen
            newPosition = getDefaultPosition()
            console.log('Orbit was off-screen, resetting to default position')
          } else {
            // Adjust position to stay within bounds
            newPosition = getSafePosition(prev.position, prev.size)
          }

          // On mobile, ensure we use appropriate size
          if (isMobile() && prev.size.mode === 'expanded') {
            newSize = { ...SIZES.normal, mode: 'normal' }
          }

          const hasChanged =
            newPosition.x !== prev.position.x ||
            newPosition.y !== prev.position.y ||
            newSize.mode !== prev.size.mode

          if (hasChanged) {
            saveState({ position: newPosition, size: newSize })
            return { ...prev, position: newPosition, size: newSize }
          }

          return prev
        })
      }, 150) // Debounce delay
    }

    const handleOrientationChange = () => {
      // Longer delay for orientation changes to allow viewport to settle
      setTimeout(handleResize, 300)
    }

    const handleVisibilityChange = () => {
      // When page becomes visible again, check if position is still valid
      if (!document.hidden) {
        handleResize()
      }
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleOrientationChange)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      clearTimeout(resizeTimeout)
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [mounted, saveState])

  // Handle mobile keyboard and scroll behavior
  useEffect(() => {
    if (!mounted || !isMobile()) return

    let initialViewportHeight = window.innerHeight
    let scrollTimeout: NodeJS.Timeout

    const handleViewportChange = () => {
      const currentHeight = window.innerHeight
      const heightDifference = initialViewportHeight - currentHeight

      // If viewport shrunk significantly (keyboard opened)
      if (heightDifference > 150) {
        if (state.isOpen) {
          // Auto-collapse on mobile when keyboard opens
          setState(prev => ({ ...prev, isOpen: false }))
          saveState({ isOpen: false })
        }

        // Move icon up to avoid keyboard
        setState(prev => ({
          ...prev,
          position: {
            ...prev.position,
            y: Math.min(prev.position.y, currentHeight - 80)
          }
        }))
      } else if (heightDifference < -50) {
        // Keyboard closed, restore position if needed
        const defaultPos = getDefaultPosition()
        setState(prev => ({
          ...prev,
          position: {
            ...prev.position,
            y: Math.max(prev.position.y, defaultPos.y)
          }
        }))
      }
    }

    const handleScroll = () => {
      // Debounce scroll events
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        // Auto-collapse on scroll for better UX
        if (state.isOpen) {
          setState(prev => ({ ...prev, isOpen: false }))
          saveState({ isOpen: false })
        }
      }, 100)
    }

    const handleTouchStart = (e: TouchEvent) => {
      // Prevent accidental drags when scrolling
      if (state.isOpen && e.touches.length === 1) {
        const touch = e.touches[0]
        const isNearIcon = Math.abs(touch.clientX - state.position.x) < 100 &&
                          Math.abs(touch.clientY - state.position.y) < 100

        if (!isNearIcon) {
          setState(prev => ({ ...prev, isOpen: false }))
          saveState({ isOpen: false })
        }
      }
    }

    window.addEventListener('resize', handleViewportChange)
    window.addEventListener('scroll', handleScroll, { passive: true })
    document.addEventListener('touchstart', handleTouchStart, { passive: true })

    return () => {
      clearTimeout(scrollTimeout)
      window.removeEventListener('resize', handleViewportChange)
      window.removeEventListener('scroll', handleScroll)
      document.removeEventListener('touchstart', handleTouchStart)
    }
  }, [mounted, state.isOpen, state.position, saveState])

  // Load persisted state from localStorage with recovery
  const loadPersistedState = useCallback(() => {
    if (typeof window === 'undefined') return

    try {
      const positionKey = getStorageKey(STORAGE_KEYS.position, orgId)
      const sizeKey = getStorageKey(STORAGE_KEYS.size, orgId)
      const isOpenKey = getStorageKey(STORAGE_KEYS.isOpen, orgId)
      const activeTabKey = getStorageKey(STORAGE_KEYS.activeTab, orgId)

      // Load and validate position
      let position = getDefaultPosition()
      const savedPosition = localStorage.getItem(positionKey)
      if (savedPosition) {
        try {
          const parsedPosition = JSON.parse(savedPosition)
          if (parsedPosition && typeof parsedPosition.x === 'number' && typeof parsedPosition.y === 'number') {
            position = parsedPosition
          }
        } catch (e) {
          console.warn('Invalid position data, using default')
        }
      }

      // Load and validate size
      let size = { ...SIZES.normal, mode: 'normal' as const }
      const savedSize = localStorage.getItem(sizeKey)
      if (savedSize) {
        try {
          const parsedSize = JSON.parse(savedSize)
          if (parsedSize && parsedSize.mode && SIZES[parsedSize.mode]) {
            size = { ...SIZES[parsedSize.mode], mode: parsedSize.mode }
          }
        } catch (e) {
          console.warn('Invalid size data, using default')
        }
      }

      // Validate position is safe for current viewport
      const safePosition = getSafePosition(position, size)

      // Load other settings
      let isOpen = false
      const savedIsOpen = localStorage.getItem(isOpenKey)
      if (savedIsOpen) {
        try {
          isOpen = JSON.parse(savedIsOpen)
        } catch (e) {
          console.warn('Invalid isOpen data, using default')
        }
      }

      let activeTab: OrbitState['activeTab'] = 'chat'
      const savedActiveTab = localStorage.getItem(activeTabKey)
      if (savedActiveTab && ['chat', 'research', 'agent'].includes(savedActiveTab)) {
        activeTab = savedActiveTab as OrbitState['activeTab']
      }

      setState(prev => ({
        ...prev,
        position: safePosition,
        size,
        isOpen,
        activeTab
      }))
    } catch (error) {
      console.warn('Failed to load Orbit state from localStorage:', error)
      // Fallback to defaults
      setState(prev => ({
        ...prev,
        position: getDefaultPosition(),
        size: { ...SIZES.normal, mode: 'normal' },
        isOpen: false,
        activeTab: 'chat'
      }))
    }
  }, [orgId])

  // Recovery function for corrupted state
  const recoverState = useCallback(() => {
    console.log('Recovering Orbit state to defaults')
    setState({
      isOpen: false,
      position: getDefaultPosition(),
      size: { ...SIZES.normal, mode: 'normal' },
      activeTab: 'chat'
    })

    // Clear corrupted data
    if (orgId) {
      try {
        Object.values(STORAGE_KEYS).forEach(key => {
          localStorage.removeItem(getStorageKey(key, orgId))
        })
      } catch (error) {
        console.warn('Failed to clear corrupted state:', error)
      }
    }
  }, [orgId])

  // Handle icon drag
  const handleIconDragStart = useCallback(() => {
    setIsDragging(true)
  }, [])

  const handleIconDragEnd = useCallback((newPosition: OrbitPosition) => {
    // Only update state after drag is complete to prevent conflicts
    setState(prev => ({ ...prev, position: newPosition }))
    saveState({ position: newPosition }, true) // Immediate save
    setIsDragging(false)
  }, [saveState])

  // Handle opening/closing
  const toggleOpen = useCallback(() => {
    setState(prev => {
      const newState = { ...prev, isOpen: !prev.isOpen }
      saveState({ isOpen: newState.isOpen })
      return newState
    })
  }, [saveState])

  // Don't render until mounted to avoid hydration issues
  if (!mounted) return null

  return (
    <div className={cn("orbit-assistant", className)}>
      {/* Floating Icon */}
      <OrbitIcon
        onClick={toggleOpen}
        position={state.position}
        isDragging={isDragging}
        onDragStart={handleIconDragStart}
        onDragEnd={handleIconDragEnd}
      />
      
      {/* Modal/Panel */}
      <AnimatePresence>
        {state.isOpen && (
          <OrbitModal
            state={state}
            setState={setState}
            saveState={saveState}
            onClose={() => toggleOpen()}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

export { OrbitIcon }
