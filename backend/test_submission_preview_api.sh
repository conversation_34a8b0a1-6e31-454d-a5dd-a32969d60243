#!/bin/bash

# Test script for the Deal Submission Preview API endpoint
# This script tests the new GET /api/v1/deals/{deal_id}/submissions/preview endpoint

echo "🧪 Testing Deal Submission Preview API Endpoint"
echo "================================================"

# Configuration
BASE_URL="http://localhost:8000"
API_ENDPOINT="/api/v1/deals"

# You'll need to replace these with actual values from your database
DEAL_ID="YOUR_DEAL_ID_HERE"
ORG_ID="YOUR_ORG_ID_HERE"
AUTH_TOKEN="YOUR_AUTH_TOKEN_HERE"

echo "📋 Test Configuration:"
echo "   Base URL: $BASE_URL"
echo "   Deal ID: $DEAL_ID"
echo "   Org ID: $ORG_ID"
echo ""

# Test 1: Valid deal with submissions
echo "🔄 Test 1: Testing valid deal submission preview..."
echo "Request: GET $BASE_URL$API_ENDPOINT/$DEAL_ID/submissions/preview"

curl -X GET \
  "$BASE_URL$API_ENDPOINT/$DEAL_ID/submissions/preview" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "X-ORG-ID: $ORG_ID" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo ""
echo "----------------------------------------"

# Test 2: Invalid deal ID format
echo "🔄 Test 2: Testing invalid deal ID format..."
INVALID_DEAL_ID="invalid-id"
echo "Request: GET $BASE_URL$API_ENDPOINT/$INVALID_DEAL_ID/submissions/preview"

curl -X GET \
  "$BASE_URL$API_ENDPOINT/$INVALID_DEAL_ID/submissions/preview" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "X-ORG-ID: $ORG_ID" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo ""
echo "----------------------------------------"

# Test 3: Non-existent deal ID
echo "🔄 Test 3: Testing non-existent deal ID..."
NONEXISTENT_DEAL_ID="507f1f77bcf86cd799439011"
echo "Request: GET $BASE_URL$API_ENDPOINT/$NONEXISTENT_DEAL_ID/submissions/preview"

curl -X GET \
  "$BASE_URL$API_ENDPOINT/$NONEXISTENT_DEAL_ID/submissions/preview" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "X-ORG-ID: $ORG_ID" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo ""
echo "----------------------------------------"

# Test 4: Missing authentication
echo "🔄 Test 4: Testing missing authentication..."
echo "Request: GET $BASE_URL$API_ENDPOINT/$DEAL_ID/submissions/preview (no auth)"

curl -X GET \
  "$BASE_URL$API_ENDPOINT/$DEAL_ID/submissions/preview" \
  -H "X-ORG-ID: $ORG_ID" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo ""
echo "----------------------------------------"

# Test 5: Missing organization header
echo "🔄 Test 5: Testing missing organization header..."
echo "Request: GET $BASE_URL$API_ENDPOINT/$DEAL_ID/submissions/preview (no org header)"

curl -X GET \
  "$BASE_URL$API_ENDPOINT/$DEAL_ID/submissions/preview" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo ""
echo "🎉 API endpoint tests completed!"
echo ""
echo "📝 Instructions for manual testing:"
echo "1. Start the backend server: uvicorn app.main:app --reload"
echo "2. Update the variables at the top of this script with real values:"
echo "   - DEAL_ID: A valid deal ID from your database"
echo "   - ORG_ID: The organization ID for the deal"
echo "   - AUTH_TOKEN: A valid JWT token for authentication"
echo "3. Run this script: ./test_submission_preview_api.sh"
echo ""
echo "📊 Expected responses:"
echo "   - Test 1: 200 OK with submission preview data"
echo "   - Test 2: 400 Bad Request (invalid ID format)"
echo "   - Test 3: 404 Not Found (deal doesn't exist)"
echo "   - Test 4: 401 Unauthorized (missing auth)"
echo "   - Test 5: 400 Bad Request (missing org header)"
