#!/usr/bin/env python3
"""
Test script to validate scoring engine with the provided thesis and submission data.
"""

import asyncio

from app.models.thesis import (
    AggregationType,
    CompoundCondition,
    ConditionOperator,
    FilterCondition,
    LogicalOperator,
    RuleType,
    ScoringRule,
)
from app.services.thesis.scoring import ComprehensiveThesis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def create_mock_thesis():
    """Create a mock thesis object with the provided rules."""
    thesis = type(
        "Thesis",
        (),
        {
            "id": "6852adc57e4cdaca6c4499b9",
            "name": "Testing Thesis",
            "scoring_rules": [],
        },
    )()

    # Rule 1: Age < 30 (scoring rule)
    rule1 = ScoringRule(
        _id="6852ae0e7e4cdaca6c4499c3",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68526d66bed831c711a69640",
        weight=2.0,
        condition=FilterCondition(
            question_id="68526d66bed831c711a69640",
            operator=ConditionOperator.LESS_THAN,
            value=30,
        ),
    )

    # Rule 2: Bonus rule with compound condition
    rule2 = ScoringRule(
        _id="6852d7b22201bde772b5750f",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.BONUS,
        bonus_points=-90.0,
        condition=CompoundCondition(
            operator=LogicalOperator.AND,
            conditions=[
                FilterCondition(
                    question_id="68526c40bed831c711a69638",
                    operator=ConditionOperator.EQUALS,
                    value=False,
                    section_id="68516493fe232b40b3ba3eb3",
                    aggregation=AggregationType.ANY,
                )
            ],
        ),
    )

    # Rule 3: Stage in [pre_seed, seed, series_a]
    rule3 = ScoringRule(
        _id="6852ec94184dcacac5cf10cb",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68516493fe232b40b3ba3eb1",
        weight=1.0,
        condition=FilterCondition(
            question_id="68516493fe232b40b3ba3eb1",
            operator=ConditionOperator.IN,
            value=["pre_seed", "seed", "series_a"],
        ),
    )

    # Rule 4: Industry in specific list
    rule4 = ScoringRule(
        _id="6852ecb8184dcacac5cf10cd",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68516493fe232b40b3ba3eb2",
        weight=1.0,
        condition=FilterCondition(
            question_id="68516493fe232b40b3ba3eb2",
            operator=ConditionOperator.IN,
            value=[
                "fintech",
                "saas",
                "healthtech",
                "edtech",
                "proptech",
                "cleantech",
                "biotech",
                "ai_ml",
                "cybersecurity",
                "mobility",
                "manufacturing",
            ],
        ),
    )

    # Rule 5: Full-time = true (scoring rule)
    rule5 = ScoringRule(
        _id="6852ecd3184dcacac5cf10d3",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68526c40bed831c711a69638",
        weight=1.0,
        condition=FilterCondition(
            question_id="68526c40bed831c711a69638",
            operator=ConditionOperator.EQUALS,
            value="true",
        ),
    )

    # Rule 6: Has experience = true
    rule6 = ScoringRule(
        _id="6852ece8184dcacac5cf10d5",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68526c8cbed831c711a6963c",
        weight=1.0,
        condition=FilterCondition(
            question_id="68526c8cbed831c711a6963c",
            operator=ConditionOperator.EQUALS,
            value="true",
        ),
    )

    # Rule 7: Has MVP = true (weight 10)
    rule7 = ScoringRule(
        _id="6852ed17184dcacac5cf10d7",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68516494fe232b40b3ba3eb8",
        weight=10.0,
        condition=FilterCondition(
            question_id="68516494fe232b40b3ba3eb8",
            operator=ConditionOperator.EQUALS,
            value="true",
        ),
    )

    # Rule 8: Text scoring for value proposition
    rule8 = ScoringRule(
        _id="6852ed9b184dcacac5cf10d9",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68516494fe232b40b3ba3ebb",
        weight=1.0,
        condition=FilterCondition(
            question_id="68516494fe232b40b3ba3ebb",
            operator=None,
            value={
                "good_reference": "Clear, Value-Oriented, Concise, Focused on User Experience and Differentiators,Feature List, But with Context",
                "bad_reference": "Too Generic / Vague, Buzzword Salad,Overly Technical for Non-Technical Readers",
            },
        ),
    )

    # Rule 9: Text scoring for biggest challenge
    rule9 = ScoringRule(
        _id="6852ee29184dcacac5cf10db",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68516495fe232b40b3ba3ecc",
        weight=1.0,
        condition=FilterCondition(
            question_id="68516495fe232b40b3ba3ecc",
            operator=None,
            value={
                "good_reference": "Demonstrates self-awareness, real problem-solving, and clear learning or growth from the experience.\n\nFocuses on a tangible, relevant obstacle and explains how the founder responded and adapted.",
                "bad_reference": "Vague, generic, or blames external factors without taking responsibility or showing growth.\n\nAvoids specifics or fails to show what was learned or improved through the challenge",
            },
        ),
    )

    # Rule 10: Has traction = true
    rule10 = ScoringRule(
        _id="6852ee3f184dcacac5cf10dd",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68516494fe232b40b3ba3ebe",
        weight=1.0,
        condition=FilterCondition(
            question_id="68516494fe232b40b3ba3ebe",
            operator=ConditionOperator.EQUALS,
            value="true",
        ),
    )

    # Rule 11: Team size > 1
    rule11 = ScoringRule(
        _id="6852ee6c184dcacac5cf10df",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="68516494fe232b40b3ba3ebf",
        weight=1.0,
        condition=FilterCondition(
            question_id="68516494fe232b40b3ba3ebf",
            operator=ConditionOperator.GREATER_THAN,
            value=1,
        ),
    )

    # Rule 12: Text scoring for vision
    rule12 = ScoringRule(
        _id="6852ef2c184dcacac5cf10fe",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.SCORING,
        question_id="6852eea3184dcacac5cf10f3",
        weight=2.0,
        condition=FilterCondition(
            question_id="6852eea3184dcacac5cf10f3",
            operator=None,
            value={
                "good_reference": "Paints a clear, ambitious, and compelling picture of the future that connects directly to the startup's mission.\n\nShows both big-picture thinking and an understanding of the steps needed to get there.",
                "bad_reference": "Generic, uninspiring, or disconnected from the actual business/problem; sounds like it could apply to any startup.\n\nLacks clarity, ambition, or a concrete link to the company's unique approach or market.",
            },
        ),
    )

    # Rule 13: Bonus rule - age < 30 with 100% percentage
    rule13 = ScoringRule(
        _id="6852f245812ead1a112ace8c",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.BONUS,
        bonus_points=10.0,
        condition=CompoundCondition(
            operator=LogicalOperator.AND,
            conditions=[
                FilterCondition(
                    question_id="68526d66bed831c711a69640",
                    operator=ConditionOperator.LESS_THAN,
                    value=30,
                    section_id="68516493fe232b40b3ba3eb3",
                    aggregation=AggregationType.PERCENTAGE,
                    aggregate_threshold=100.0,
                )
            ],
        ),
    )

    # Rule 14: Bonus rule - compound condition
    rule14 = ScoringRule(
        _id="6852f3dc812ead1a112ace8e",
        thesis_id="6852adc57e4cdaca6c4499b9",
        rule_type=RuleType.BONUS,
        bonus_points=10.0,
        condition=CompoundCondition(
            operator=LogicalOperator.AND,
            conditions=[
                FilterCondition(
                    question_id="68526c8cbed831c711a6963c",
                    operator=ConditionOperator.EQUALS,
                    value=True,
                    section_id="68516493fe232b40b3ba3eb3",
                    aggregation=AggregationType.COUNT,
                    aggregate_threshold=2.0,
                ),
                FilterCondition(
                    question_id="68516493fe232b40b3ba3eb1",
                    operator=ConditionOperator.IN,
                    value=["pre_seed", "seed", "series_a"],
                ),
            ],
        ),
    )

    thesis.scoring_rules = [
        rule1,
        rule2,
        rule3,
        rule4,
        rule5,
        rule6,
        rule7,
        rule8,
        rule9,
        rule10,
        rule11,
        rule12,
        rule13,
        rule14,
    ]
    return thesis


def create_mock_submission():
    """Create a mock submission with the provided answers."""
    return {
        "68516493fe232b40b3ba3eb1": "seed",
        "68516493fe232b40b3ba3eaf": "Banchod",
        "68516493fe232b40b3ba3eb0": "www.banchod.com",
        "68516493fe232b40b3ba3eb2": ["proptech", "ai_ml"],
        "6852eea3184dcacac5cf10f3": "To transform the property management into seamless experience",
        "68516493fe232b40b3ba3eb4_0": "Mr.A",
        "68516493fe232b40b3ba3eb5_0": ["ceo"],
        "68526d66bed831c711a69640_0": 25,
        "6851a9581f178c8cafa5d77c_0": ["male"],
        "685267efbed831c711a69631_0": 40,
        "68526c40bed831c711a69638_0": True,
        "68526c8cbed831c711a6963c_0": True,
        "68516493fe232b40b3ba3eb4_1": "Mr.B",
        "68516493fe232b40b3ba3eb5_1": ["cto"],
        "68526d66bed831c711a69640_1": 26,
        "6851a9581f178c8cafa5d77c_1": ["male"],
        "685267efbed831c711a69631_1": 40,
        "68526c40bed831c711a69638_1": True,
        "68526c8cbed831c711a6963c_1": True,
        "68516493fe232b40b3ba3eb4_2": "Ms.C",
        "68516493fe232b40b3ba3eb5_2": ["cfo"],
        "68526d66bed831c711a69640_2": 29,
        "6851a9581f178c8cafa5d77c_2": ["trans"],
        "685267efbed831c711a69631_2": 20,
        "68516494fe232b40b3ba3eb8": True,
        "68516494fe232b40b3ba3eb9": "web_app",
        "68516494fe232b40b3ba3eba": "Python – for backend services, AI/ML models, and data pipelines  TypeScript – for frontend with Next.js and server-side rendering  Go (Golang) – for high-concurrency microservices (e.g., notifications, queues)  Bash/SQL – for DevOps and data querying  Rust (exploratory) – for future performance-critical modules The stack is built for long-term scalability, with clean separation of services and an async-first mindset.",
        "68516494fe232b40b3ba3ebc": "Frontend: Built with Next.js (App Router) using TypeScript, TailwindCSS, and ShadCN components. SSR is enabled for SEO-critical views. Light/dark theming is controlled at the ThemeProvider level.\n\nBackend:\n\nFastAPI (Python) handles all API logic – form submissions, AI scoring, investor workflows.\n\nRedis (with redis.asyncio) powers an async job queue for background processing – e.g., AI enrichment, notifications.\n\nMongoDB stores structured form data, submissions, and user metadata.\n\nPostgreSQL is used for relational data like user/org management, payments, and audit logs.\n\nAI Layer:\n\nCustom agentic workflows (OpenAI, LangChain + orchestration logic) used for deal evaluation, summarization, and matching to investor theses.\n\nEmbeddings (OpenAI or Cohere) are stored in Weaviate/Qdrant vector DBs for semantic search and intelligent filtering.\n\nDevOps & Infra:\n\nDeployed on AWS (EC2 + RDS + S3)\n\nDockerized microservices, reverse-proxied through NGINX\n\nCI/CD via GitHub Actions, observability via Sentry and Prometheus/Grafana\n\nSecurity:\n\nTenant isolation by org\n\nRole-based access control\n\nAll file uploads use S3 pre-signed URLs, and user data is encrypted in transit and at rest\n\nThis architecture is modular, async, and built to scale across multiple investment workflows and geographies.",
        "68516494fe232b40b3ba3ebe": True,
        "68516494fe232b40b3ba3ebf": 33,
        "68516495fe232b40b3ba3ec7": "2024-02-06T17:00:00.000Z",
        "68516495fe232b40b3ba3ecd": "referral",
        "68516495fe232b40b3ba3ecc": 'Our biggest challenge was training the AI to handle the nuance in tenant communication.\n\nProperty management isn\'t just maintenance tickets — it\'s emotional, contextual, and often vague. A message like "it smells weird again" could mean mold, plumbing, or trash — and the system needs to triage that correctly, not just tag it as "low priority."\n\nWe had to build a robust NLP pipeline that not only classifies intent but also learns from context — historical tickets, location-specific issues, tenant sentiment, even weather data. And we had to integrate this seamlessly with real-world ops — like syncing with vendors, lease timelines, and building access rules.\n\nMaking this all work in real-time, at scale, across different property types — without frustrating tenants or overloading managers — was hard. But now, it\'s our strongest differentiator.',
    }


async def test_scoring():
    """Test the scoring engine with the provided data."""
    print("🧪 Testing Comprehensive Thesis Scoring Engine")
    print("=" * 60)

    # Create mock data
    thesis = create_mock_thesis()
    submission = create_mock_submission()

    # Initialize scoring engine
    engine = ComprehensiveThesisScoringEngine()

    # Calculate score
    result = await engine.calculate_comprehensive_score(thesis, submission)

    # Print results
    print("\n📊 SCORING RESULTS:")
    print(f"Total Score: {result['thesis']['total_score']}")
    print(f"Normalized Score: {result['thesis']['normalized_score']:.1f}%")
    print(f"Max Possible Score: {result['thesis']['max_possible_score']}")

    print("\n📋 QUESTION SCORES:")
    for qid, score_data in result["thesis"]["question_scores"].items():
        print(
            f"  {qid}: {score_data['weighted_score']:.1f} / {score_data['weight']:.1f} - {score_data['explanation']}"
        )

    print("\n🎁 BONUS SCORES:")
    for bonus_id, bonus_data in result["thesis"]["bonus_scores"].items():
        print(
            f"  {bonus_id}: {bonus_data['bonus_points']:.1f} points - {bonus_data['reason']}"
        )

    print("\n📈 SCORING DETAILS:")
    for detail in result["thesis"]["scoring_details"]:
        print(
            f"  Rule {detail['rule_id']}: {detail['weighted_score']:.1f} points - {detail['explanation']}"
        )

    print("\n🔍 METADATA:")
    print(f"  Rules Processed: {result['metadata']['total_rules_processed']}")
    print(f"  AI Scoring Used: {result['metadata']['ai_scoring_used']}")

    return result


if __name__ == "__main__":
    asyncio.run(test_scoring())
